{"name": "@open-swe/shared", "homepage": "https://github.com/langchain-ai/open-swe/blob/main/README.md", "repository": {"type": "git", "url": "https://github.com/langchain-ai/open-swe.git"}, "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "rm -rf ./dist .turbo || true", "build": "yarn clean && tsc", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "test": "NODE_OPTIONS=--experimental-vm-modules yarn run jest --config jest.config.js --testPathIgnorePatterns=int.test.ts", "test:int": "node --experimental-vm-modules node_modules/jest/bin/jest.js --config jest.config.js --testPathPattern=int.test.ts", "test:single": "NODE_OPTIONS=--experimental-vm-modules yarn run jest --config jest.config.js --testTimeout 100000"}, "dependencies": {"@langchain/core": "^0.3.65", "@langchain/langgraph": "^0.3.8", "@langchain/langgraph-sdk": "^0.0.95", "@octokit/rest": "^22.0.0", "jsonwebtoken": "^9.0.2", "zod": "^3.25.32"}, "devDependencies": {"@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.19.0", "@jest/globals": "^29.7.0", "@octokit/types": "^12.0.0", "@tsconfig/recommended": "^1.0.8", "@types/jest": "^29.5.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.13.5", "dotenv": "^16.4.7", "eslint": "^9.19.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.7.0", "prettier": "^3.5.2", "ts-jest": "^29.1.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0"}, "packageManager": "yarn@3.5.1", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}, "./*": {"types": "./dist/*.d.ts", "import": "./dist/*.js", "require": "./dist/*.js"}, "./package.json": "./package.json"}, "files": ["dist/**/*"]}