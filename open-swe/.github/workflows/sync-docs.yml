name: Sync Documentation Files

on:
  push:
    branches:
      - main
    paths:
      - 'apps/docs/**'
  workflow_dispatch:

jobs:
  sync-docs:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout source repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Check for MDX files and images in apps/docs
      id: check-files
      run: |
        docs_found=false
        images_found=false
        
        if find apps/docs -name "*.mdx" -type f | head -1 | grep -q .; then
          docs_found=true
          echo "Found MDX files to sync"
        fi
        
        if [ -d "apps/docs/images" ] && find apps/docs/images -type f | head -1 | grep -q .; then
          images_found=true
          echo "Found images to sync"
        fi
        
        if [ "$docs_found" = true ] || [ "$images_found" = true ]; then
          echo "docs_found=true" >> $GITHUB_OUTPUT
          echo "At least MDX files or images found to sync"
        else
          echo "docs_found=false" >> $GITHUB_OUTPUT
          echo "No MDX files or images found in apps/docs"
        fi
        
        echo "images_found=$images_found" >> $GITHUB_OUTPUT

    - name: Clone LangChain docs repository
      if: steps.check-files.outputs.docs_found == 'true'
      run: |
        git clone https://x-access-token:${{ secrets.LANGCHAIN_DOCS_TOKEN }}@github.com/langchain-ai/docs.git langchain-docs
        cd langchain-docs
        git config user.name "OpenSWE Bot"
        git config user.email "<EMAIL>"

    - name: Create branch for documentation sync
      if: steps.check-files.outputs.docs_found == 'true'
      run: |
        cd langchain-docs
        BRANCH_NAME="sync-openswe-docs-$(date +%Y%m%d-%H%M%S)"
        echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
        git checkout -b "$BRANCH_NAME"

    - name: Prepare target directories
      if: steps.check-files.outputs.docs_found == 'true'
      run: |
        cd langchain-docs
        # Remove existing content to ensure clean sync
        rm -rf src/labs/swe
        mkdir -p src/labs/swe
        # Prepare images directory
        mkdir -p src/images

    - name: Copy MDX files to LangChain docs
      if: steps.check-files.outputs.docs_found == 'true'
      run: |
        # Copy all .mdx files from apps/docs to the target directory, preserving structure
        if find apps/docs -name "*.mdx" -type f | head -1 | grep -q .; then
          find apps/docs -name "*.mdx" -type f | while read file; do
            # Get the relative path from apps/docs
            rel_path="${file#apps/docs/}"
            # Create the directory structure in target
            target_dir="langchain-docs/src/labs/swe/$(dirname "$rel_path")"
            mkdir -p "$target_dir"
            # Copy the file
            cp "$file" "langchain-docs/src/labs/swe/$rel_path"
          done
          
          # List copied files for verification
          echo "Copied MDX files:"
          find langchain-docs/src/labs/swe/ -name "*.mdx" -type f
        else
          echo "No MDX files found to copy"
        fi
        
        echo "Directory structure:"
        tree langchain-docs/src/labs/swe/ || find langchain-docs/src/labs/swe/ -type d

    - name: Copy images to LangChain docs
      if: steps.check-files.outputs.docs_found == 'true' && steps.check-files.outputs.images_found == 'true'
      run: |
        # Copy all images from apps/docs/images to langchain-docs/src/images
        if [ -d "apps/docs/images" ]; then
          cp -r apps/docs/images/* langchain-docs/src/images/
          
          # List copied images for verification
          echo "Copied images:"
          find langchain-docs/src/images/ -type f
        else
          echo "No images directory found to copy"
        fi

    - name: Commit changes
      if: steps.check-files.outputs.docs_found == 'true'
      id: commit
      run: |
        cd langchain-docs
        git add src/labs/swe/
        git add src/images/
        
        if git diff --staged --quiet; then
          echo "No changes to commit"
          echo "has_changes=false" >> $GITHUB_OUTPUT
        else
          commit_message="Sync OpenSWE documentation files
          
          - Updated MDX files from apps/docs/"
          
          if [ "${{ steps.check-files.outputs.images_found }}" = "true" ]; then
            commit_message="$commit_message
          - Updated images from apps/docs/images/"
          fi
          
          commit_message="$commit_message
          - Synced at $(date -u '+%Y-%m-%d %H:%M:%S UTC')
          - Source commit: ${{ github.sha }}"
          
          git commit -m "$commit_message"
          echo "has_changes=true" >> $GITHUB_OUTPUT
        fi

    - name: Push branch and create pull request
      if: steps.check-files.outputs.docs_found == 'true' && steps.commit.outputs.has_changes == 'true'
      run: |
        cd langchain-docs
        git push origin "$BRANCH_NAME"
        
        # Create pull request using GitHub CLI
        gh pr create \
          --title "Sync OpenSWE Documentation Files" \
          --body "This PR syncs documentation files from the OpenSWE repository.
          
          **Changes:**
          - Updated MDX files from \`apps/docs/\`
          - Target directory: \`src/labs/swe/\`$([ "${{ steps.check-files.outputs.images_found }}" = "true" ] && echo "
          - Updated images from \`apps/docs/images/\`
          - Target directory: \`src/images/\`" || echo "")
          - Source commit: ${{ github.sha }}
          - Synced at $(date -u '+%Y-%m-%d %H:%M:%S UTC')
          
          **Auto-generated by:** OpenSWE Documentation Sync Workflow" \
          --head "$BRANCH_NAME" \
          --base main
      env:
        GH_TOKEN: ${{ secrets.LANGCHAIN_DOCS_TOKEN }}
