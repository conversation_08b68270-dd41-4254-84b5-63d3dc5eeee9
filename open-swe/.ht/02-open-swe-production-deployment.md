# Open SWE 生产环境部署和服务启动方式

## 概述

Open SWE 是一个基于 LangGraph 的 AI 代理系统，包含两个主要组件：
1. **Agent 服务** (`apps/open-swe`) - 基于 LangGraph 的 AI 代理后端
2. **Web 应用** (`apps/web`) - Next.js 前端界面

## 开发环境 vs 生产环境

### 开发环境
- **Agent 服务**: `yarn dev` 启动 LangGraph 开发服务器 (端口 2024)
- **Web 应用**: `yarn dev` 启动 Next.js 开发服务器 (端口 3000)

### 生产环境

## Agent 服务生产部署

### 1. 部署平台
Open SWE 使用 **LangGraph Platform** 进行生产部署，这是一个托管的 LangGraph 服务。

### 2. 部署流程

#### 自动部署 (CI/CD)
- 通过 GitHub Actions 自动部署
- 触发条件：`main` 分支的 `apps/open-swe/**` 路径变更
- 部署脚本：`apps/open-swe/scripts/deploy-langgraph.ts`

#### 部署配置
```json
// langgraph.json
{
  "node_version": "20",
  "graphs": {
    "programmer": "./apps/open-swe/src/graphs/programmer/index.ts:graph",
    "planner": "./apps/open-swe/src/graphs/planner/index.ts:graph", 
    "manager": "./apps/open-swe/src/graphs/manager/index.ts:graph"
  },
  "env": "./apps/open-swe/.env",
  "dependencies": ["./apps/open-swe"],
  "auth": {
    "path": "./apps/open-swe/src/security/auth.ts:auth"
  },
  "http": {
    "app": "./apps/open-swe/src/routes/app.ts:app"
  }
}
```

#### 部署步骤
1. **构建**: `yarn build` (TypeScript 编译)
2. **部署**: 调用 LangGraph Platform API
3. **配置更新**: 更新部署配置指向最新代码
4. **等待部署**: 监控部署状态直到完成

### 3. 服务启动方式

#### LangGraph Platform 托管
- 服务由 LangGraph Platform 自动启动和管理
- 基于 `langgraph.json` 配置启动多个图 (programmer, planner, manager)
- 自动处理负载均衡、扩展和监控

#### 认证和路由
- **认证**: 通过 `apps/open-swe/src/security/auth.ts` 处理
- **路由**: 通过 `apps/open-swe/src/routes/app.ts` 定义 API 端点
- **Webhook**: 处理 GitHub 事件 (`/webhooks/github`)

## Web 应用生产部署

### 1. 部署方式
- 使用 Next.js 标准部署流程
- 支持 Vercel、Netlify 等平台部署

### 2. 构建和启动
```bash
# 构建
yarn build

# 启动生产服务器
yarn start
```

### 3. API 代理
Web 应用通过 API 代理与 Agent 服务通信：
- 代理配置：`apps/web/src/app/api/[..._path]/route.ts`
- 目标 URL：`LANGGRAPH_API_URL` 环境变量
- 加密：API 密钥通过 `SECRETS_ENCRYPTION_KEY` 加密

## 环境变量配置

### Agent 服务必需环境变量
```bash
# LangSmith 追踪
LANGCHAIN_PROJECT="default"
LANGCHAIN_API_KEY="lsv2_pt_..."
LANGCHAIN_TRACING_V2="true"

# LLM 提供商
ANTHROPIC_API_KEY=""  # 推荐
OPENAI_API_KEY=""     # 可选
GOOGLE_API_KEY=""     # 可选

# 基础设施
DAYTONA_API_KEY=""    # 云端沙盒

# 工具
FIRECRAWL_API_KEY=""  # URL 内容提取

# GitHub App
GITHUB_APP_ID=""
GITHUB_APP_PRIVATE_KEY=""
```

### Web 应用必需环境变量
```bash
# API URLs
NEXT_PUBLIC_API_URL=""
LANGGRAPH_API_URL=""

# 加密密钥
SECRETS_ENCRYPTION_KEY=""

# GitHub App OAuth
NEXT_PUBLIC_GITHUB_APP_CLIENT_ID=""
GITHUB_APP_CLIENT_SECRET=""
GITHUB_APP_REDIRECT_URI=""
```

## 架构总结

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   GitHub App    │    │   Web App        │    │   Agent Service │
│   (OAuth/Webhook)│◄──►│   (Next.js)      │◄──►│   (LangGraph)   │
│                 │    │   Port: 3000     │    │   Port: 2024    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   LangGraph      │
                       │   Platform       │
                       │   (Production)   │
                       └──────────────────┘
```

## 关键特点

1. **分离式架构**: Web 应用和 Agent 服务独立部署
2. **托管服务**: Agent 服务使用 LangGraph Platform 托管
3. **自动部署**: 通过 GitHub Actions 实现 CI/CD
4. **API 代理**: Web 应用通过代理与 Agent 服务通信
5. **安全认证**: 支持多种认证方式 (GitHub OAuth, API Key, Webhook)
6. **环境隔离**: 开发和生产环境使用不同的配置和部署方式

## 监控和日志

- **LangSmith**: 用于追踪和监控 AI 代理的执行
- **GitHub Actions**: 部署状态监控
- **LangGraph Platform**: 服务健康状态和性能监控
