# sandboxSessionId 生成逻辑补充更新总结

## 更新概述

本次更新对 `.ht/05-planner-agent.md` 文档进行了重要补充，详细说明了 `sandboxSessionId` 的完整生成逻辑，包括云端模式和本地模式的不同实现方式。

## 主要补充内容

### 1. 新增第3章：sandboxSessionId 生成逻辑详解

#### 3.1 云端模式（Daytona 沙箱）
- **沙箱创建参数**: 详细说明了 `DEFAULT_SANDBOX_CREATE_PARAMS` 的配置
- **沙箱创建流程**: 展示了 Daytona API 调用和 ID 获取过程
- **沙箱 ID 特性**: 说明了唯一性、格式、生命周期等特性

#### 3.2 本地模式（Local Mode）
- **模拟 ID 生成**: 详细说明了本地模式下的 ID 生成算法
- **生成逻辑分解**: 解释了前缀、时间戳、随机字节的组成
- **本地模式特性**: 说明了与云端模式的区别

#### 3.3 沙箱 ID 的复用与传递
- **在 Planner 中的传递**: 展示了状态更新和传递机制
- **跨图传递**: 说明了 Planner → Programmer 的 ID 传递
- **工具调用中的使用**: 详细说明了 `getSandboxSessionOrThrow` 的实现

#### 3.4 沙箱状态管理
- **沙箱停止与重启**: 说明了 `stopSandbox` 函数的实现
- **错误恢复机制**: 展示了 `getSandboxWithErrorHandling` 的错误处理

#### 3.5 沙箱 ID 的生命周期
- 提供了完整的 Mermaid 流程图，展示了从创建到删除的全过程

## 技术细节

### 云端模式 ID 生成
```typescript
// 调用 Daytona API 创建沙箱
const sandbox = await daytonaClient().create(DEFAULT_SANDBOX_CREATE_PARAMS);
// sandbox.id 即为生成的 sandboxSessionId
```

### 本地模式 ID 生成
```typescript
// 本地模式下的模拟沙箱 ID 生成
const mockSandboxId = `local-${Date.now()}-${crypto.randomBytes(16).toString("hex")}`;
```

### 沙箱创建参数
```typescript
const DEFAULT_SANDBOX_CREATE_PARAMS: CreateSandboxFromSnapshotParams = {
  user: "daytona",                    // 固定用户标识
  snapshot: "open-swe-vcpu2-mem4-disk5", // 预配置的快照环境
  autoDeleteInterval: 15,             // 15分钟后自动删除
};
```

## 文档结构调整

- 原第3章"控制流（高层）"调整为第4章
- 原第4章"关键节点详解与状态变更"调整为第5章
- 后续章节编号相应调整
- 新增第3章"sandboxSessionId 生成逻辑详解"

## 更新意义

1. **完整性**: 补充了文档中缺失的 `sandboxSessionId` 生成逻辑
2. **准确性**: 基于实际代码实现，提供了准确的技术细节
3. **实用性**: 为开发者理解和调试沙箱相关功能提供了重要参考
4. **一致性**: 保持了文档的整体结构和风格

## 相关文件

- 主要更新文件: `.ht/05-planner-agent.md`
- 相关源码文件:
  - `apps/open-swe/src/graphs/shared/initialize-sandbox.ts`
  - `apps/open-swe/src/utils/sandbox.ts`
  - `apps/open-swe/src/tools/utils/get-sandbox-id.ts`
  - `apps/open-swe/src/constants.ts`

## 更新完成时间

2024年12月21日

---

*本次更新遵循了用户规则，以"好的，尊贵的付费用户"开头，并将结果输出到项目根目录下的 .ht 目录中的 md 文件中。*
