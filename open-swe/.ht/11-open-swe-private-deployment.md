# Open SWE 私有化部署方案

## 概述

虽然 Open SWE 官方推荐使用 LangGraph Platform 进行生产部署，但项目本身支持私有化部署。本文档详细介绍如何在不依赖 LangGraph Platform 的情况下，实现 Open SWE 的私有化部署。

## 私有化部署架构

### 1. 架构对比

#### 官方部署架构
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   GitHub App    │    │   Web App        │    │   Agent Service │
│   (OAuth/Webhook)│◄──►│   (Next.js)      │◄──►│   (LangGraph)   │
│                 │    │   Port: 3000     │    │   Port: 2024    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   LangGraph      │
                       │   Platform       │
                       │   (托管服务)      │
                       └──────────────────┘
```

#### 私有化部署架构
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   GitHub App    │    │   Web App        │    │   Agent Service │
│   (OAuth/Webhook)│◄──►│   (Next.js)      │◄──►│   (LangGraph)   │
│                 │    │   Port: 3000     │    │   Port: 2024    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   私有服务器      │
                       │   (自托管)       │
                       └──────────────────┘
```

## 私有化部署方案

### 方案一：直接部署到服务器

#### 1. 服务器要求
- **操作系统**: Linux (Ubuntu 20.04+ 推荐)
- **Node.js**: 20.x
- **内存**: 至少 4GB RAM
- **存储**: 至少 10GB 可用空间
- **网络**: 公网 IP 或域名

#### 2. 部署步骤

##### 步骤 1: 准备服务器环境
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Node.js 20
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 Yarn
corepack enable
corepack prepare yarn@3.5.1 --activate

# 安装 PM2 (进程管理)
npm install -g pm2
```

##### 步骤 2: 部署代码
```bash
# 克隆项目
git clone https://github.com/langchain-ai/open-swe.git
cd open-swe

# 安装依赖
yarn install

# 构建项目
yarn build
```

##### 步骤 3: 配置环境变量
```bash
# Agent 服务配置
cp apps/open-swe/.env.example apps/open-swe/.env
nano apps/open-swe/.env

# Web 应用配置
cp apps/web/.env.example apps/web/.env
nano apps/web/.env
```

##### 步骤 4: 启动服务 (生产环境推荐方式)

**⚠️ 重要**: 不要在生产环境使用 `yarn dev` 命令，这是开发模式，包含热重载和调试功能。

**推荐的生产环境启动方式**:

###### 方式 A: 使用 LangGraph CLI 的 `up` 命令 (推荐)
```bash
# 进入 Agent 服务目录
cd apps/open-swe

# 使用 LangGraph CLI 的生产模式启动
langgraphjs up --port 2024 --config ../../langgraph.json

# 或者使用 Docker 模式 (更安全)
langgraphjs up --port 2024 --config ../../langgraph.json --wait
```

###### 方式 B: 使用 LangGraph CLI 的 `build` 命令构建 Docker 镜像
```bash
# 构建生产镜像
langgraphjs build --tag open-swe-agent:latest --config ../../langgraph.json

# 运行容器
docker run -d \
  --name open-swe-agent \
  -p 2024:2024 \
  --env-file .env \
  open-swe-agent:latest
```

###### 方式 C: 使用 PM2 启动编译后的代码
```bash
# 先构建项目
yarn build

# 使用 PM2 启动生产服务器
pm2 start "node dist/server.js" --name "open-swe-agent" \
  --env production \
  --instances max \
  --max-memory-restart 1G

# 启动 Web 应用
cd ../web
pm2 start "yarn start" --name "open-swe-web"

# 保存 PM2 配置
pm2 save
pm2 startup
```

### 方案二：Docker 容器化部署

#### 1. 使用 LangGraph CLI 生成 Dockerfile (推荐)

**⚠️ 重要**: 不要手动创建 Dockerfile，使用 LangGraph CLI 自动生成更安全、更可靠的配置。

```bash
# 生成 Dockerfile 和相关文件
langgraphjs dockerfile ./Dockerfile --add-docker-compose --config ../../langgraph.json
```

这会自动生成：
- `Dockerfile` - 优化的生产环境镜像
- `docker-compose.yml` - 完整的服务编排
- `.dockerignore` - 排除不必要的文件

#### 2. 手动创建 Dockerfile (备选方案)

##### Agent 服务 Dockerfile
```dockerfile
# apps/open-swe/Dockerfile
FROM node:20-alpine

WORKDIR /app

# 安装依赖
COPY package.json yarn.lock ./
RUN corepack enable && corepack prepare yarn@3.5.1 --activate
RUN yarn install --frozen-lockfile

# 复制源代码
COPY . .

# 构建项目
RUN yarn build

# 暴露端口
EXPOSE 2024

# 启动服务 (生产环境)
CMD ["langgraphjs", "up", "--port", "2024", "--config", "../../langgraph.json"]
```

##### Web 应用 Dockerfile
```dockerfile
# apps/web/Dockerfile
FROM node:20-alpine

WORKDIR /app

# 安装依赖
COPY package.json yarn.lock ./
RUN corepack enable && corepack prepare yarn@3.5.1 --activate
RUN yarn install --frozen-lockfile

# 复制源代码
COPY . .

# 构建项目
RUN yarn build

# 暴露端口
EXPOSE 3000

# 启动服务
CMD ["yarn", "start"]
```

#### 2. Docker Compose 配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  agent:
    build:
      context: ./apps/open-swe
      dockerfile: Dockerfile
    ports:
      - "2024:2024"
    environment:
      - NODE_ENV=production
    env_file:
      - ./apps/open-swe/.env
    restart: unless-stopped

  web:
    build:
      context: ./apps/web
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - LANGGRAPH_API_URL=http://agent:2024
    env_file:
      - ./apps/web/.env
    depends_on:
      - agent
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
      - agent
    restart: unless-stopped
```

#### 3. Nginx 反向代理配置
```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream web_app {
        server web:3000;
    }

    upstream agent_service {
        server agent:2024;
    }

    server {
        listen 80;
        server_name your-domain.com;

        # Web 应用
        location / {
            proxy_pass http://web_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Agent 服务 API
        location /api/ {
            proxy_pass http://agent_service/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

### 方案三：Kubernetes 部署

#### 1. 创建 Kubernetes 配置文件

##### Agent 服务 Deployment
```yaml
# k8s/agent-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: open-swe-agent
spec:
  replicas: 2
  selector:
    matchLabels:
      app: open-swe-agent
  template:
    metadata:
      labels:
        app: open-swe-agent
    spec:
      containers:
      - name: agent
        image: your-registry/open-swe-agent:latest
        ports:
        - containerPort: 2024
        env:
        - name: NODE_ENV
          value: "production"
        envFrom:
        - configMapRef:
            name: agent-config
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
---
apiVersion: v1
kind: Service
metadata:
  name: open-swe-agent-service
spec:
  selector:
    app: open-swe-agent
  ports:
  - port: 2024
    targetPort: 2024
  type: ClusterIP
```

##### Web 应用 Deployment
```yaml
# k8s/web-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: open-swe-web
spec:
  replicas: 2
  selector:
    matchLabels:
      app: open-swe-web
  template:
    metadata:
      labels:
        app: open-swe-web
    spec:
      containers:
      - name: web
        image: your-registry/open-swe-web:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: LANGGRAPH_API_URL
          value: "http://open-swe-agent-service:2024"
        envFrom:
        - configMapRef:
            name: web-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: open-swe-web-service
spec:
  selector:
    app: open-swe-web
  ports:
  - port: 3000
    targetPort: 3000
  type: LoadBalancer
```

## 环境变量配置

### Agent 服务必需环境变量
```bash
# LangSmith 追踪 (可选，私有化部署可以不使用)
LANGCHAIN_PROJECT="default"
LANGCHAIN_API_KEY=""
LANGCHAIN_TRACING_V2="false"

# LLM 提供商 (至少需要一个)
ANTHROPIC_API_KEY="your-anthropic-key"
OPENAI_API_KEY="your-openai-key"
GOOGLE_API_KEY="your-google-key"

# 基础设施
DAYTONA_API_KEY="your-daytona-key"

# 工具
FIRECRAWL_API_KEY="your-firecrawl-key"

# GitHub App
GITHUB_APP_ID="your-github-app-id"
GITHUB_APP_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----\n...\n-----END RSA PRIVATE KEY-----"

# 本地模式 (私有化部署推荐)
OPEN_SWE_LOCAL_MODE="true"
```

### Web 应用必需环境变量
```bash
# API URLs
NEXT_PUBLIC_API_URL="https://your-domain.com/api"
LANGGRAPH_API_URL="https://your-domain.com/api"

# 加密密钥
SECRETS_ENCRYPTION_KEY="your-encryption-key"

# GitHub App OAuth
NEXT_PUBLIC_GITHUB_APP_CLIENT_ID="your-github-app-client-id"
GITHUB_APP_CLIENT_SECRET="your-github-app-client-secret"
GITHUB_APP_REDIRECT_URI="https://your-domain.com/api/auth/github/callback"

# GitHub App 详情
GITHUB_APP_NAME="your-github-app-name"
GITHUB_APP_ID="your-github-app-id"
GITHUB_APP_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----\n...\n-----END RSA PRIVATE KEY-----"
```

## 私有化部署的优势

### 1. 数据安全
- **数据本地化**: 所有数据存储在私有服务器上
- **网络隔离**: 不依赖外部云服务
- **访问控制**: 完全控制访问权限

### 2. 成本控制
- **无托管费用**: 不支付 LangGraph Platform 费用
- **资源可控**: 根据实际需求配置服务器资源
- **长期成本**: 一次性投入，长期使用

### 3. 定制化
- **功能定制**: 可以根据需求修改代码
- **集成灵活**: 更容易与现有系统集成
- **版本控制**: 完全控制版本更新

## 私有化部署的挑战

### 1. 运维复杂度
- **服务器管理**: 需要自行管理服务器
- **监控告警**: 需要搭建监控系统
- **备份恢复**: 需要制定备份策略

### 2. 扩展性限制
- **手动扩展**: 需要手动处理负载扩展
- **资源管理**: 需要合理规划资源使用
- **性能优化**: 需要自行优化性能

### 3. 技术支持
- **问题排查**: 需要自行排查问题
- **更新维护**: 需要自行处理更新
- **安全补丁**: 需要及时应用安全补丁

## 推荐部署方案

### 小规模部署 (1-10 用户)
- **方案**: 直接部署到服务器
- **服务器**: 单台 4-8GB RAM 服务器
- **成本**: 每月 $20-50

### 中等规模部署 (10-100 用户)
- **方案**: Docker 容器化部署
- **服务器**: 2-4 台服务器 + 负载均衡
- **成本**: 每月 $100-300

### 大规模部署 (100+ 用户)
- **方案**: Kubernetes 集群部署
- **基础设施**: 云原生架构
- **成本**: 每月 $500+

## 监控和运维

### 1. 监控指标
- **服务健康状态**: HTTP 状态码、响应时间
- **资源使用**: CPU、内存、磁盘、网络
- **业务指标**: 请求量、错误率、用户活跃度

### 2. 日志管理
- **集中日志**: 使用 ELK Stack 或类似方案
- **日志轮转**: 定期清理和归档日志
- **告警配置**: 设置关键指标告警

### 3. 备份策略
- **代码备份**: Git 仓库备份
- **数据备份**: 数据库和配置文件备份
- **灾难恢复**: 制定恢复流程

## LangGraph CLI 命令详解

### 开发模式 vs 生产模式

#### 开发模式 (`langgraphjs dev`)
- **用途**: 本地开发和调试
- **特点**: 
  - 热重载 (文件变化自动重启)
  - 调试模式
  - 开发工具集成
  - 内存存储 (重启后数据丢失)
  - 自动打开浏览器
  - 文件监听和自动重启
- **适用场景**: 开发、测试、调试
- **⚠️ 警告**: 不适用于生产环境

#### 生产模式 (`langgraphjs up`)
- **用途**: 生产环境部署
- **特点**:
  - 持久化存储
  - 多进程支持
  - 健康检查
  - 生产级日志
  - Docker 容器化
  - 自动重启和恢复
  - 负载均衡支持
- **适用场景**: 生产环境、正式部署

### 关键命令说明

#### `langgraphjs up` - 生产环境启动
```bash
langgraphjs up [options]

选项:
  -c, --config <path>          配置文件路径 (默认: 当前目录)
  -p, --port <port>            端口号 (默认: 8123)
  -d, --docker-compose <path>  自定义 docker-compose.yml 路径
  --recreate                    强制重建容器和卷
  --no-pull                     使用本地镜像 (默认拉取最新镜像)
  --watch                       文件变化时重启
  --wait                        等待服务启动后返回
  --postgres-uri <uri>          PostgreSQL 连接 URI
```

#### `langgraphjs build` - 构建 Docker 镜像
```bash
langgraphjs build [options]

选项:
  -t, --tag <tag>              镜像标签 (必需)
  -c, --config <path>          配置文件路径
  --no-pull                     不拉取基础镜像
```

#### `langgraphjs dockerfile` - 生成 Dockerfile
```bash
langgraphjs dockerfile <save-path> [options]

选项:
  --add-docker-compose          同时生成 docker-compose.yml
  -c, --config <path>          配置文件路径
```

### 生产环境最佳实践

#### 1. 使用 `langgraphjs up` 进行生产部署
```bash
# 基本生产部署
langgraphjs up --port 2024 --config ../../langgraph.json

# 带健康检查的生产部署
langgraphjs up --port 2024 --config ../../langgraph.json --wait

# 使用自定义 PostgreSQL
langgraphjs up --port 2024 --config ../../langgraph.json \
  --postgres-uri "********************************/db"
```

#### 2. 构建生产镜像
```bash
# 构建镜像
langgraphjs build --tag open-swe-agent:latest --config ../../langgraph.json

# 推送到镜像仓库
docker tag open-swe-agent:latest your-registry/open-swe-agent:latest
docker push your-registry/open-swe-agent:latest
```

#### 3. 使用 Docker Compose 编排
```bash
# 生成完整的 Docker Compose 配置
langgraphjs dockerfile ./Dockerfile --add-docker-compose --config ../../langgraph.json

# 启动服务
docker-compose up -d
```

### 安全考虑

#### 1. 环境变量管理
- 使用 `.env` 文件存储敏感信息
- 在生产环境使用环境变量或密钥管理服务
- 避免在代码中硬编码敏感信息

#### 2. 网络安全
- 使用反向代理 (Nginx) 进行 SSL 终止
- 配置防火墙规则
- 限制容器网络访问

#### 3. 容器安全
- 使用非 root 用户运行容器
- 定期更新基础镜像
- 扫描镜像漏洞

### 监控和日志

#### 1. 健康检查
```bash
# 检查服务健康状态
curl http://localhost:2024/ok

# 检查详细状态
curl http://localhost:2024/health
```

#### 2. 日志管理
```bash
# 查看容器日志
docker logs open-swe-agent

# 查看 PM2 日志
pm2 logs open-swe-agent
```

#### 3. 性能监控
- 监控 CPU、内存使用率
- 监控 API 响应时间
- 监控错误率和成功率

## 总结

Open SWE 支持多种私有化部署方案，可以根据实际需求选择合适的方案。私有化部署提供了更好的数据安全性和控制力，但也增加了运维复杂度。建议在部署前充分评估技术能力和运维成本。

**重要提醒**:
1. **生产环境不要使用 `yarn dev`** - 这是开发模式，包含热重载和调试功能
2. **推荐使用 `langgraphjs up`** - 这是专门为生产环境设计的启动方式
3. **使用 LangGraph CLI 生成配置** - 避免手动创建 Dockerfile，使用官方工具生成更安全
4. **配置健康检查和监控** - 确保服务的稳定性和可靠性
