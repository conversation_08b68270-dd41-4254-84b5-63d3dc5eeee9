### Open SWE Reviewer 子图 逻辑流转与状态变更全解析

本篇延续 Planner/Programmer 文档风格，系统梳理 Reviewer 子图（审查阶段）的控制流、节点职责、状态变量、关键路由与典型场景；附 Mermaid 可视化与关键源码摘录，便于理解如何在只读模式下高效审查并给出改进行动。

---

## 1. Reviewer 的目标与产出

- 目标: 在只读模式下对 Programmer 执行结果进行审查，根据计划、用户请求与变更集判断是否满足需求；必要时提出新增/调整动作，或直接判定通过。
- 主要产出:
  - 审查过程的只读工具输出（写入 `messages` 与 `reviewerMessages`）
  - 最终审查结论：
    - 若通过：标记完成（供 Programmer 的路由判断）
    - 若不通过：生成 `additional_actions`，合并到 `taskPlan`（仅保留已完成项 + 新增项），并写回 GitHub Issue（非本地模式）
  - 审查轮次计数 `reviewsCount`（溢出后由 Programmer 侧路由到结论）

---

## 2. Reviewer 的状态（ReviewerGraphState）要点

- `internalMessages`：来自 Programmer 的内部历史，用于提供上下文给 Reviewer
- `reviewerMessages`：Reviewer 子图内部消息（独立计数审查动作）
- `messages`：用户可见消息（含审查工具输出）
- `sandboxSessionId`、`targetRepository`、`branchName`、`codebaseTree`
- `taskPlan`：用于展示已完成项与当前活动项
- `baseBranchName`：基准分支，用于 diff
- `changedFiles`：通过 `git diff {BASE_BRANCH_NAME} --name-only` 获取的变更文件列表
- `customRules`、`dependenciesInstalled`
- `reviewsCount`：审查轮次
- `tokenData`：模型 token 数据

---

## 3. 控制流（高层）

```mermaid
graph TD
  START((START)) --> A[initialize-state]
  A --> B[generate-review-actions]
  B -->|AI含tool_calls| C[take-review-actions]
  B -->|无tool_calls| D[final-review]
  C -->|需诊断| E[diagnose-reviewer-error]
  C -->|未超限| B
  C -->|超限| D
  E --> B
  D --> END
```

源码对应：

```30:53:apps/open-swe/src/graphs/reviewer/index.ts
const workflow = new StateGraph(ReviewerGraphStateObj, GraphConfiguration)
  .addNode("initialize-state", initializeState)
  .addNode("generate-review-actions", generateReviewActions)
  .addNode("take-review-actions", takeReviewerActions, { ends: ["generate-review-actions","diagnose-reviewer-error","final-review"] })
  .addNode("diagnose-reviewer-error", diagnoseError)
  .addNode("final-review", finalReview)
  .addEdge(START, "initialize-state")
  .addEdge("initialize-state", "generate-review-actions")
  .addConditionalEdges("generate-review-actions", takeReviewActionsOrFinalReview, ["take-review-actions", "final-review"]) 
  .addEdge("diagnose-reviewer-error", "generate-review-actions")
  .addEdge("final-review", END)
```

---

## 4. 关键节点与状态变更

### 4.1 initialize-state
- 恢复/创建沙箱，获取 `baseBranchName`（从 git 配置或目标分支名），生成 `changedFiles` 列表，追加“审查开始”隐藏消息（AI+ToolMessage）。
- 更新：`baseBranchName`、`changedFiles`、`messages`（审查开始事件）、`codebaseTree?`、`dependenciesInstalled?`。
- 转移：`generate-review-actions`。

### 4.2 generate-review-actions（生成审查动作）
- 工具（只读）：`grep`、`shell`、`view`、`install_dependencies`、`scratchpad`。
- 提示：包含 `codebaseTree`、`changedFiles`、`customRules`、`dependencies_installed`、`CURRENT_WORKING_DIRECTORY`、计划摘要与用户请求；若有上次 Code Review 结果，追加“上一轮审查上下文”。
- 更新：将 LLM 响应写入 `messages` 与 `reviewerMessages`；记录 `tokenData`。
- 转移：若响应含 `tool_calls` → `take-review-actions`，否则 → `final-review`。

### 4.3 take-review-actions（执行只读审查动作）
- 执行 `grep/shell/view/install_dependencies/scratchpad` 工具；本地模式对命令进行安全过滤
- 若沙箱模式且检测到改动，则提交分支并可能更新 PR 号（写入用户可见 PR 提示消息）
- 合并依赖状态，并更新 `codebaseTree`（若可）
- 超限控制：`maxReviewActions*2`（默认 60）条 AI/ToolMessage 计数超限则进入 `final-review`
- 若需诊断错误 → `diagnose-reviewer-error`，否则 → 回到 `generate-review-actions`

### 4.4 diagnose-reviewer-error（审查动作错误诊断）
- 复用 shared 诊断节点（基于最近失败工具、树与历史生成诊断说明）
- 转移：`generate-review-actions`

### 4.5 final-review（最终审查）
- 工具：`code_review_mark_task_completed` 与 `code_review_mark_task_not_complete`
- 若“通过”：写入“完成”消息，结束（不修改计划）
- 若“不通过”：将 `additional_actions` 追加为新的剩余计划（仅保留已完成项 + 新增项），写回 GitHub Issue（非本地模式），`reviewsCount+1`
- 更新：`taskPlan?`、`messages/internalMessages/reviewerMessages`、`tokenData?`

---

## 5. 关键路由与守卫条件

- 生成或执行审查动作：取决于 `generate-review-actions` 的响应是否含 `tool_calls`
- 执行动作后的路由：
  - 若需错误诊断 → `diagnose-reviewer-error`
  - 若未超限 → `generate-review-actions`
  - 若超限（AI/ToolMessage ≥ `maxReviewActions*2`）→ `final-review`

---

## 6. 典型场景与状态变化轨迹

### 场景 A：一次审查即通过
1) `initialize-state` → 2) `generate-review-actions`（无 tool_calls）→ 3) `final-review` 标记完成 → `END`

关键状态：
- `changedFiles` 收集到 PR 变更集
- `reviewerMessages` 仅含最终审查消息

### 场景 B：需要多轮只读审查
1) `initialize-state` → 2) `generate-review-actions` → 3) `take-review-actions`（多轮 grep/shell/view）→ 4) 达到“足够上下文”或超限 → `final-review`

### 场景 C：提出新的改进行动
1) 按 B 收集上下文 → 2) `final-review` 判断“不通过”，输出 `additional_actions` → 更新 `taskPlan` 并写回 Issue → 3) 返回 Programmer 子图继续执行

---

## 7. 模型与工具分工

- 模型：
  - `LLMTask.REVIEWER`：`generate-review-actions`、`final-review`
  - `LLMTask.SUMMARIZER`（共享）：`diagnose-reviewer-error`

- 工具：
  - `grep`、`view`、`shell`、`install_dependencies`、`scratchpad`
  - final-review 工具对：`code_review_mark_task_completed` / `code_review_mark_task_not_complete`

---

## 8. 调试与排障建议

- 如始终不进入 `final-review`：检查是否持续产生 `tool_calls` 且未超限；可调小 `maxReviewActions`
- 如 `changedFiles` 为空：确认基准分支名是否正确（`baseBranchName`），并检查获取逻辑
- 若本地模式命令被过滤：查看命令安全过滤逻辑；必要时调整命令
- 若追加 `additional_actions` 后未回到 Programmer：检查 Programmer 侧路由（`routeGenerateActionsOrEnd`）是否正确接收更新的 `taskPlan`

---

以上为 Reviewer 子图的全流程与状态说明。建议与 Programmer 文档联读，以理解“审查—执行”的闭环联动与回路边界。


