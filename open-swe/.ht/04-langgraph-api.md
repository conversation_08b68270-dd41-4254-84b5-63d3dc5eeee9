## langgraph API

### addNode & addEdge & Command

我先在代码库里全局搜索 `addNode` 的定义与用法，确认它的函数签名和第三个参数的含义，同时查看 `apps/open-swe/src/graphs/manager/index.ts` 相关上下文。随后我会并行检索关键词和函数定义以加快定位。

- 两个参数的 `addNode(name, fn)`：只注册节点及其实现。下一步的流转需要用 `addEdge` 显式连接，或由其他节点的动态跳转进入该节点。
- 三个参数的 `addNode(name, fn, options)`：第三个参数是配置项，常用的是 `ends: [...]`，用于“声明”该节点可能通过动态跳转到达的目的地列表。节点内部若返回 `Command({ goto: 'some-node' })`，运行时会直接跳转到 `ends` 中声明的目标。这不是 `addEdge`，不会创建静态边，只是做可达性/校验的声明。

结合你当前文件的写法：

```16:26:apps/open-swe/src/graphs/manager/index.ts
const workflow = new StateGraph(ManagerGraphStateObj, GraphConfiguration)
  .addNode("initialize-github-issue", initializeGithubIssue)
  .addNode("classify-message", classifyMessage, {
    ends: [END, "start-planner", "create-new-session"],
  })
  .addNode("create-new-session", createNewSession)
  .addNode("start-planner", startPlanner)
  .addEdge(START, "initialize-github-issue")
  .addEdge("initialize-github-issue", "classify-message")
  .addEdge("create-new-session", END)
  .addEdge("start-planner", END);
```

- `classify-message` 通过第三参的 `ends` 声明：它可动态跳到 `END`、`start-planner`、`create-new-session`。从这个节点到这些目的地的跳转由节点返回的 `Command({ goto })` 触发，不需要 `addEdge`。
- 但被跳转到的下游节点（如 `start-planner`、`create-new-session`）后续如何结束/流转，仍用 `addEdge` 明确连接（如它们各自到 `END` 的静态边）。

简要结论
- 第三个参数不是 `addEdge` 的替代；它是为“动态跳转”做声明与校验。
- 静态流程（固定起止/顺序）用 `addEdge`；需要节点内部按逻辑分流时，用 `ends` + `Command({ goto })`。

### messages 合并策略

我先打开并定位 `apps/open-swe/src/graphs/planner/nodes/prepare-state.ts` 第116行附近查看 `commandUpdate` 的实现，然后并行查找 Planner 的 State 定义（聚合/合并策略）以确认 `messages` 的合并/覆盖行为依据。

我先打开 `prepare-state.ts` 第116行附近以及 `packages/shared/src/open-swe/planner/types.ts` 和 `packages/shared/src/open-swe/types.ts`，确认 `messages` 在 `Command({ update })` 中的合并策略和 reducer 定义。

**结论**：会“合并”而不是整体覆盖。具体是通过 LangGraph 的 messages reducer 增量应用：新增消息会追加，`RemoveMessage` 会删除对应 `id` 的旧消息。

**依据**
- `PlannerGraphStateObj` 继承 `MessagesZodState`，其 `messages` 字段使用 LangGraph 默认的 `messagesStateReducer` 做增量合并，而非简单替换。
```14:21:packages/shared/src/open-swe/planner/types.ts
export const PlannerGraphStateObj = MessagesZodState.extend({
  sandboxSessionId: withLangGraph(z.string(), {
    reducer: {
      schema: z.string(),
      fn: (_state, update) => update,
    },
  }),
```
- 你提到的第 116 行 `commandUpdate` 构造了 `RemoveMessage` 和新增消息列表，如果是“覆盖”就不需要 `RemoveMessage` 这类操作符。
```116:129:apps/open-swe/src/graphs/planner/nodes/prepare-state.ts
  const commandUpdate: PlannerGraphUpdate = {
    messages: [
      ...removedNonSummaryMessages,
      ...(summaryMessage ? [summaryMessage] : []),
      ...untrackedComments,
    ],
    // Reset plan context summary as it's now included in the messages array.
    contextGatheringNotes: "",
  };

  return new Command({
    update: commandUpdate,
    goto: "initialize-sandbox",
  });
```

- 真实效果：保留原有人类消息，移除“非 summary 的非人类消息”，可选插入一条 `summaryMessage`，并追加未跟踪的评论消息；整体为增量变更，而非覆盖。

- 补充参考：项目里对消息 reducer 的使用方式（虽用于 `internalMessages`，但体现相同合并语义）。
```163:177:packages/shared/src/open-swe/types.ts
export const GraphAnnotation = MessagesZodState.extend({
  /**
   * The internal messages. These are the messages which are
   * passed to the LLM, truncated, removed etc. The main `messages`
   * key is never modified to persist the content show on the client.
   */
  internalMessages: withLangGraph(z.custom<BaseMessage[]>(), {
    reducer: {
      schema: z.custom<Messages>(),
      fn: messagesStateReducer,
    },
    jsonSchemaExtra: {
      langgraph_type: "messages",
    },
    default: () => [],
  }),
```

- 简单说：`messages` 字段的更新是“操作列表”（添加/删除），不是“整数组替换”。

- 记录
  - 我已定位 `prepare-state.ts` 第116行并核对 `PlannerGraphStateObj` 基于 `MessagesZodState` 的定义，确认采用 LangGraph 的消息增量合并语义。

- 小结
  - 第116行的 `messages` 更新不会覆盖历史，而是通过 `messagesStateReducer` 按新增/删除进行合并。
