## useThreadsSWR

文件 `apps/web/src/hooks/useThreadsSWR.ts` 实现了线程列表的 SWR 缓存与数据流。

### 核心实现逻辑概览
- 值类型与参数
  - 支持多种图状态联合类型 `AnyGraphState`（Manager/Planner/Reviewer/通用）。
  - 入参 `options`：
    - `assistantId`：按某个 graph 过滤线程。
    - 刷新行为：`refreshInterval`、`revalidateOnFocus`、`revalidateOnReconnect`（默认取 `THREAD_SWR_CONFIG`）。
    - GitHub 安装筛选：`currentInstallation`、`disableOrgFiltering`。
    - 分页：`limit`(默认25)、`offset`(默认0)、`sortBy`(默认`updated_at`)、`sortOrder`(默认`desc`)。

- SWR 缓存键
  - 使用 `assistantId` 与分页参数构造稳定的 `swrKey`，确保分页/筛选变化触发正确的缓存与请求。

- 数据获取 fetcher
  - 读取 `NEXT_PUBLIC_API_URL`，通过 `createClient` 创建 SDK 客户端。
  - 生成 `searchArgs`：
    - 若有 `assistantId`：`metadata.graph_id = assistantId`，并合并分页参数。
    - 否则仅使用分页参数或 `undefined`。
  - 调用 `client.threads.search<TGraphState>(searchArgs)` 拉取线程列表。

- SWR 配置
  - 结合 `THREAD_SWR_CONFIG` 的 `errorRetryCount`、`errorRetryInterval`、`dedupingInterval` 与传入的刷新选项，得到 `data/error/isLoading/isValidating/mutate`。

- 数据后处理与组织
  - `threads` 通过 `useMemo`：
    - `disableOrgFiltering = true`：直接返回所有线程。
    - 否则：
      - 若没有任何返回数据 → `setHasMoreState(false)`。
      - 若没有 `currentInstallation` → `setHasMoreState(false)` 并返回空数组。
      - 其余情况：按 `thread.metadata.installation_name === currentInstallation.accountName` 过滤只保留当前安装对应的线程。
  - `hasMore`：当且仅当 `hasMoreState` 为真且当前 `threads` 非空。

- 返回值
  - `{ threads, error, isLoading, isValidating, mutate, hasMore }`

执行进度：已完成文件阅读与逻辑梳理，无阻塞。
