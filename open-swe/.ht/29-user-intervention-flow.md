# Open-SWE 用户介入处理流程详细分析

## 1. 概述

Open-SWE 中的用户介入处理流程主要发生在两个关键阶段：
1. **Planner 阶段**：用户对生成的计划进行审核、修改或拒绝
2. **Programmer 阶段**：在执行过程中用户对计划进行动态调整

## 2. Planner 阶段的用户介入流程

### 2.1 计划生成与中断

**触发点**：`interruptProposedPlan` 函数
- 位置：`apps/open-swe/src/graphs/planner/nodes/proposed-plan.ts:177-379`
- 时机：Planner 完成计划生成后，需要用户确认

**中断机制**：
```typescript
const interruptResponse = interrupt<HumanInterrupt, HumanResponse[] | HumanResponse>({
  action_request: {
    action: PLAN_INTERRUPT_ACTION_TITLE,
    args: { plan: proposedPlan.join(`\n${PLAN_INTERRUPT_DELIMITER}\n`) }
  },
  config: {
    allow_accept: true,    // 允许接受
    allow_edit: true,      // 允许编辑
    allow_respond: true,   // 允许回复
    allow_ignore: true     // 允许忽略
  },
  description: `A new plan has been generated for your request...`
});
```

### 2.2 用户响应类型处理

#### 2.2.1 接受计划 (accept)
```typescript
if (humanResponse.type === "accept") {
  planItems = proposedPlan.map((p, index) => ({
    index, plan: p, completed: false
  }));
  
  runInput.taskPlan = createNewTask(userTaskRequest, state.proposedPlanTitle, planItems, {
    existingTaskPlan: state.taskPlan
  });
  
  // GitHub 评论更新（非本地模式）
  if (!isLocalMode(config) && state.githubIssueId) {
    await postGitHubIssueComment({
      commentBody: `### ✅ Plan Accepted ✅\n\nProceeding to implementation...`
    });
  }
}
```

#### 2.2.2 编辑计划 (edit)
```typescript
else if (humanResponse.type === "edit") {
  const editedPlan = (humanResponse.args as ActionRequest).args.plan
    .split(PLAN_INTERRUPT_DELIMITER)
    .map((step: string) => step.trim());
    
  planItems = editedPlan.map((p: string, index: number) => ({
    index, plan: p, completed: false
  }));
  
  // 创建新的任务计划
  runInput.taskPlan = createNewTask(userTaskRequest, state.proposedPlanTitle, planItems, {
    existingTaskPlan: state.taskPlan
  });
}
```

#### 2.2.3 回复计划 (response)
```typescript
if (humanResponse.type === "response") {
  // 路由到 determine-needs-context 节点
  return new Command({ goto: "determine-needs-context" });
}
```

#### 2.2.4 忽略计划 (ignore)
```typescript
if (humanResponse.type === "ignore") {
  // 结束流程
  return new Command({ goto: END });
}
```

### 2.3 CLI 端的用户介入处理

**位置**：`apps/cli/src/index.tsx`

**状态管理**：
```typescript
const [plannerFeedback, setPlannerFeedback] = useState<string | null>(null);
const [streamingPhase, setStreamingPhase] = useState<
  "streaming" | "awaitingFeedback" | "done"
>("streaming");
```

**用户输入处理**：
```typescript
const PlannerFeedbackInput: React.FC = () => {
  const [selectedOption, setSelectedOption] = useState<"approve" | "deny" | null>(null);
  
  useInput((inputChar: string, key: { [key: string]: any }) => {
    if (key.return && selectedOption) {
      setPlannerFeedback(selectedOption);
    } else if (key.leftArrow) {
      setSelectedOption("approve");
    } else if (key.rightArrow) {
      setSelectedOption("deny");
    }
  });
  
  // 渲染选择界面
  return (
    <Box flexDirection="row" alignItems="center" gap={2}>
      <Text>Plan feedback: </Text>
      <Text color={selectedOption === "approve" ? "black" : "white"}>
        {selectedOption === "approve" ? "▶ " : "  "}Approve
      </Text>
      <Text color={selectedOption === "deny" ? "black" : "white"}>
        {selectedOption === "deny" ? "▶ " : "  "}Deny
      </Text>
    </Box>
  );
};
```

**反馈提交**：
```typescript
// apps/cli/src/utils.ts:18-104
export async function submitFeedback({
  plannerFeedback,
  plannerThreadId,
  setLogs,
  setPlannerFeedback,
  setStreamingPhase,
}) {
  const stream = await client.runs.stream(plannerThreadId, PLANNER_GRAPH_ID, {
    command: {
      resume: [{
        type: plannerFeedback === "approve" ? "accept" : "ignore",
        args: null,
      }],
    },
    streamMode: OPEN_SWE_STREAM_MODE as StreamMode[],
  });
  
  // 处理流式响应，包括可能的 Programmer 会话
  for await (const chunk of stream) {
    // 检查是否有 Programmer 会话
    if (chunkData?.programmerSession?.threadId) {
      // 加入 Programmer 流
      for await (const programmerChunk of client.runs.joinStream(
        chunkData.programmerSession.threadId,
        chunkData.programmerSession.runId,
      )) {
        // 处理 Programmer 输出
      }
    }
  }
}
```

### 2.4 Web 端的用户介入处理

**位置**：`apps/web/src/components/thread/agent-inbox/hooks/useProposedPlan.tsx`

**计划编辑状态管理**：
```typescript
export function useProposedPlan(originalPlanItems: PlanItem[], stream: ReturnType<typeof useStream>) {
  const [planItems, setPlanItems] = useState<PlanItem[]>(originalPlanItems);
  const [changesMade, setChangesMade] = useState(false);
  
  // 检测计划是否被修改
  useEffect(() => {
    setChangesMade(JSON.stringify(originalPlanItems) !== JSON.stringify(planItems));
  }, [originalPlanItems, planItems]);
}
```

**计划恢复处理**：
```typescript
const handleResumePlan = () => {
  let resume: HumanResponse[];
  if (changesMade) {
    // 如果有修改，发送编辑请求
    resume = [{
      type: "edit",
      args: {
        action: PLAN_INTERRUPT_ACTION_TITLE,
        args: { plan: convertPlanItemsToInterruptString(planItems) }
      }
    }];
  } else {
    // 如果没有修改，发送接受请求
    resume = [{ type: "accept", args: null }];
  }
  
  stream.submit({}, {
    command: { resume },
    config: { recursion_limit: 400 },
    streamResumable: true,
  });
};
```

## 3. Programmer 阶段的用户介入流程

### 3.1 计划更新触发

**位置**：`apps/open-swe/src/graphs/programmer/nodes/update-plan.ts`

**触发条件**：
- Programmer 在执行过程中发现需要调整计划
- 用户主动要求修改计划
- 遇到错误需要重新规划

### 3.2 计划更新流程

#### 3.2.1 更新原因分析
```typescript
const updatePlanReasoningTool = {
  name: "update_plan_reasoning",
  description: "Analyze the current situation and determine what changes need to be made to the plan",
  schema: z.object({
    update_plan_reasoning: z.string().describe("Detailed reasoning for why the plan needs to be updated")
  })
};
```

#### 3.2.2 计划重写
```typescript
export async function updatePlan(state: GraphState, config: GraphConfig): Promise<GraphUpdate> {
  // 获取当前活跃任务和计划项
  const activeTask = getActiveTask(state.taskPlan);
  const activePlanItems = activeTask.planRevisions.find(
    (pr) => pr.revisionIndex === activeTask.activeRevisionIndex,
  )?.plans;
  
  // 使用 LLM 重写计划
  const response = await modelWithTools.invoke([{
    role: "system", content: systemPrompt
  }, {
    role: "user", content: userMessage
  }]);
  
  // 解析新的计划项
  const { plan } = toolCall.args as z.infer<typeof updatePlanToolSchema>;
  const completedPlanItems = activePlanItems.filter((item) => item.completed);
  const newPlanItems: PlanItem[] = [
    ...completedPlanItems,
    ...plan.map((p, index) => ({
      index: totalCompletedPlanItems + index,
      plan: p,
      completed: false,
      summary: undefined,
    })),
  ];
  
  // 更新任务计划
  const newTaskPlan = updateTaskPlanItems(state.taskPlan, activeTask.id, newPlanItems, "agent");
  
  // 更新 GitHub Issue（非本地模式）
  await addTaskPlanToIssue({
    githubIssueId: state.githubIssueId,
    targetRepository: state.targetRepository,
  }, config, newTaskPlan);
}
```

### 3.3 计划重写规则

**系统提示词约束**：
```typescript
const systemPrompt = `You MUST adhere to the following criteria when generating the plan:
- Make as few changes as possible to the tasks, while still following the users request.
- You are only allowed to update plan items which are remaining, including the current task.
- Plan items which have already been completed are not allowed to be modified.
- To update the plan, you MUST pass every updated/added/untouched plan item to the \`update_plan\` tool.
- To remove an item from the plan, you should not include it in the \`update_plan\` tool call.`;
```

## 4. 用户介入的完整流程总结

### 4.1 Planner 阶段流程

1. **计划生成** → Planner 生成 `proposedPlan`
2. **中断等待** → `interruptProposedPlan` 中断到用户
3. **用户响应** → 用户选择 accept/edit/respond/ignore
4. **处理响应**：
   - `accept` → 创建 `taskPlan` → 启动 Programmer
   - `edit` → 解析编辑内容 → 创建新 `taskPlan` → 启动 Programmer
   - `respond` → 路由到 `determine-needs-context` → 可能需要更多上下文收集
   - `ignore` → 结束流程
5. **GitHub 集成** → 更新 Issue 评论（非本地模式）

### 4.2 Programmer 阶段流程

1. **执行监控** → Programmer 在执行过程中监控状态
2. **更新触发** → 检测到需要更新计划
3. **原因分析** → 使用 LLM 分析为什么需要更新
4. **计划重写** → 基于分析结果重写计划
5. **状态更新** → 更新 `taskPlan` 和 GitHub Issue
6. **继续执行** → 基于新计划继续执行

### 4.3 跨阶段协调

- **状态同步**：通过 `taskPlan` 在不同阶段间同步计划状态
- **流式处理**：CLI 和 Web 端都支持流式响应处理
- **错误恢复**：支持在出错时重新规划
- **版本控制**：计划修改会创建新的修订版本

## 5. 关键技术特点

### 5.1 中断机制
- 使用 LangGraph 的 `interrupt` 机制实现用户介入
- 支持多种响应类型：accept/edit/respond/ignore
- 配置化的权限控制

### 5.2 状态管理
- 通过 `GraphState` 管理整个流程状态
- 计划项的版本控制和修订历史
- 完成状态的跟踪

### 5.3 流式处理
- 支持实时流式响应
- 跨会话的流式连接（Planner → Programmer）
- 错误处理和恢复机制

### 5.4 多端支持
- CLI 端：命令行交互，支持键盘导航
- Web 端：图形界面，支持可视化编辑
- 统一的响应格式和处理逻辑

这种设计确保了用户在整个开发过程中都能保持对计划的控制，同时支持动态调整和错误恢复，提供了灵活而强大的用户介入机制。
