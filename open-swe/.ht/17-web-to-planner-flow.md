## Web 输入到 Planner 生成与用户修改的端到端流程（结合源码与示例）

本文说明用户在 Web 页面输入问题后，Open SWE 如何处理请求、Planner 如何生成计划、用户修改如何影响后续流程。包含关键源码引用、参数与场景示例，便于关联实际代码。

### 1. 用户在 Web 输入问题，进入 Manager 的“消息分类”

Web 端把用户输入包装成 HumanMessage 并进入 Manager 图中的 `classifyMessage`。该节点会：
- 读取最新一条人类消息与当前线程上下文（Planner/Programmer 运行状态、Issue 中的 `taskPlan`/`proposedPlan` 等）；
- 构建路由提示词并调用路由模型工具 `respond_and_route`；
- 根据路由结果决定下一步：创建 Issue、继续 Planner、恢复 Planner，或 no_op。

关键代码（提取用户消息正文并交给路由模型）：
```131:135:apps/open-swe/src/graphs/manager/nodes/classify-message/index.ts
      content: extractContentWithoutDetailsFromIssueBody(
        getMessageContentString(userMessage.content),
      ),
```

若状态中存在 `githubIssueId`，优先从 Issue 读取最新计划（保持以远端事实为准）：
```89:94:apps/open-swe/src/graphs/manager/nodes/classify-message/index.ts
  const issuePlans = state.githubIssueId
    ? await getPlansFromIssue(state, config)
    : null;
  const taskPlan = issuePlans?.taskPlan ?? state.taskPlan;
```

当需要、且尚未创建 Issue 时，会创建一个新的 GitHub Issue，并把用户原始消息替换为带 `githubIssueId` 和 `isOriginalIssue` 的版本：
```204:229:apps/open-swe/src/graphs/manager/nodes/classify-message/index.ts
    const newIssue = await createIssue({
      owner: state.targetRepository.owner,
      repo: state.targetRepository.repo,
      title,
      body: formatContentForIssueBody(body),
      githubAccessToken,
    });
    ...
    newMessages.push(
      ...[
        new RemoveMessage({ id: userMessage.id ?? "" }),
        new HumanMessage({
          ...userMessage,
          additional_kwargs: { githubIssueId: githubIssueId, isOriginalIssue: true },
        }),
      ],
    );
```

当已有 Issue 且用户追加了多条消息，会把未入库的消息补写为 Issue 评论，并根据路由/状态决定是否恢复 Planner 或进入 `start-planner`：
```232:331:apps/open-swe/src/graphs/manager/nodes/classify-message/index.ts
  const messagesNotInIssue = state.messages
    .filter(isHumanMessage)
    .filter((message) => !message.additional_kwargs?.githubIssueId);
  ... // 逐条创建评论，并替换为带 githubIssueCommentId 的消息
  if (plannerStatus === "interrupted") { /* 恢复 planner 运行 */ }
  if (toolCallArgs.route === "start_planner_for_followup") { goto = "start-planner"; }
```

场景示例（无 Issue → 自动创建）：
- 输入：用户在 Web 输入“请实现登录接口并返回用户信息”
- 结果：`classifyMessage` 创建 Issue，替换原始消息（打上 `isOriginalIssue`），然后根据路由进入下一步（常见是 `start-planner`）。

### 2. 进入 Planner，生成 Proposed Plan

Manager 触发 `start-planner` 节点，创建 Planner 线程/运行：
```62:71:apps/open-swe/src/graphs/manager/nodes/start-planner.ts
    const runInput: PlannerGraphUpdate = {
      githubIssueId: state.githubIssueId,
      targetRepository: state.targetRepository,
      taskPlan: state.taskPlan,
      branchName: state.branchName ?? getBranchName(config),
      autoAcceptPlan: state.autoAcceptPlan,
      ...(followupMessage || localMode ? { messages: [followupMessage] } : {}),
    };
```
```73:91:apps/open-swe/src/graphs/manager/nodes/start-planner.ts
    const run = await langGraphClient.runs.create(
      plannerThreadId,
      PLANNER_GRAPH_ID,
      { /* 省略：配置与流式设置 */ },
    );
```

在 Planner 内，`generate-plan` 节点调用规划模型生成 `proposedPlanTitle` 与 `proposedPlan`：
```143:147:apps/open-swe/src/graphs/planner/nodes/generate-plan/index.ts
  return {
    messages: [response, toolResponse],
    proposedPlanTitle: proposedPlanArgs.title,
    proposedPlan: proposedPlanArgs.plan,
    ...(newSessionId && { sandboxSessionId: newSessionId }),
  };
```

随后在 `interruptProposedPlan` 中：
- 若 `autoAcceptPlan` 为真，自动接受并直接进入实现；
- 否则把 Proposed Plan 写回 Issue，发布“待审核”评论，并向前端发起可交互的中断（允许 accept/edit/response/ignore）。

写回候选方案与发送提醒评论：
```251:266:apps/open-swe/src/graphs/planner/nodes/proposed-plan.ts
  await addProposedPlanToIssue({ githubIssueId: state.githubIssueId, targetRepository: state.targetRepository }, config, proposedPlan);
  await postGitHubIssueComment({ /* 评论内容：Plan Ready for Approval */ });
```

中断等待用户操作（allow_accept/edit/respond/ignore）：
```270:288:apps/open-swe/src/graphs/planner/nodes/proposed-plan.ts
  const interruptResponse = interrupt<HumanInterrupt, HumanResponse | HumanResponse[]>({
    action_request: { action: PLAN_INTERRUPT_ACTION_TITLE, args: { plan: proposedPlan.join(`\n${PLAN_INTERRUPT_DELIMITER}\n`) } },
    config: { allow_accept: true, allow_edit: true, allow_respond: true, allow_ignore: true },
    description: `A new plan has been generated ...`
  });
```

场景示例（生成并等待用户审核）：
- 输入：用户提出功能需求；
- 结果：Planner 生成标题与步骤，Issue 中新增“候选方案 JSON 标签段”和“待审核”评论，前端弹出“接受/编辑/忽略/回复”的交互。

### 3. 用户“接受计划”或“编辑计划”，如何影响流程？

在 `interruptProposedPlan`：
- 接受计划（accept）：把 `proposedPlan` 映射为 `planItems`，基于 `createNewTask` 生成新的 `taskPlan`；
- 编辑计划（edit）：从用户编辑的文本中按分隔符切分，映射为 `planItems` 并同样生成新的 `taskPlan`；
- 两种情况下都会：
  - 给 Issue 发送“已接受/已编辑并提交”的评论；
  - 调用 `startProgrammerRun` 进入实现阶段，并把 `taskPlan` 写回 Issue（JSON + 标签）。

接受计划（核心片段）：
```309:331:apps/open-swe/src/graphs/planner/nodes/proposed-plan.ts
  if (humanResponse.type === "accept") {
    planItems = proposedPlan.map((p, index) => ({ index, plan: p, completed: false }));
    runInput.taskPlan = createNewTask(userTaskRequest, state.proposedPlanTitle, planItems, { existingTaskPlan: state.taskPlan });
    if (!isLocalMode(config) && state.githubIssueId) {
      await postGitHubIssueComment({ /* 评论：Plan Accepted */ });
    }
  }
```

编辑计划（核心片段）：
```332:356:apps/open-swe/src/graphs/planner/nodes/proposed-plan.ts
  else if (humanResponse.type === "edit") {
    const editedPlan = (humanResponse.args as ActionRequest).args.plan
      .split(PLAN_INTERRUPT_DELIMITER)
      .map((step: string) => step.trim());
    planItems = editedPlan.map((p, index) => ({ index, plan: p, completed: false }));
    runInput.taskPlan = createNewTask(userTaskRequest, state.proposedPlanTitle, planItems, { existingTaskPlan: state.taskPlan });
    if (!isLocalMode(config) && state.githubIssueId) {
      await postGitHubIssueComment({ /* 评论：Plan Edited & Submitted */ });
    }
  }
```

进入实现（Programmer）并把最终 TaskPlan 写回 Issue：
```151:172:apps/open-swe/src/graphs/planner/nodes/proposed-plan.ts
  if (!isLocalMode(config)) {
    await addTaskPlanToIssue(
      { githubIssueId: state.githubIssueId, targetRepository: state.targetRepository },
      config,
      runInput.taskPlan,
    );
  }
  return new Command({ goto: END, update: { programmerSession: { ... }, taskPlan: runInput.taskPlan, messages: newMessages } });
```

场景示例（用户编辑计划再执行）：
- 输入：用户把“步骤2：实现登录 API”修改为“步骤2：实现登录 API 与 OAuth 支持”；
- 结果：Planner 将修改后的步骤切分并转为新的 `taskPlan`，在 Issue 评论中标记为“Plan Edited & Submitted”，并启动 Programmer 执行。Issue 正文中的 `<open-swe-do-not-edit-task-plan>` 标签段会被覆盖为新的 JSON。

### 4. 跟 Issue 的读写约定（理解“为何能恢复上下文”）

Open SWE 通过在 Issue 正文中嵌入特殊标签，实现“无损存储/读取”计划：
- 写入 Proposed Plan：
```326:358:apps/open-swe/src/utils/github/issue-task.ts
export async function addProposedPlanToIssue(...) { /* 使用 insertPlanToIssueBody 写回 proposedPlan JSON */ }
```
- 写入 TaskPlan：
```391:419:apps/open-swe/src/utils/github/issue-task.ts
export async function addTaskPlanToIssue(...) { /* 使用 insertPlanToIssueBody 写回 taskPlan JSON */ }
```
- 读取 TaskPlan/ProposedPlan：
```197:229:apps/open-swe/src/utils/github/issue-task.ts
export async function getPlansFromIssue(...) { /* 从 Issue 正文解析两个标签段 */ }
```

因此，即使服务重启或跨进程，系统都能从 Issue 恢复 `taskPlan`/`proposedPlan`，保证流程一致性。

### 5. 常用参数示例（从 UI 发起到 Planner）

Manager → start-planner（关键 input）：
```ts
// 来自 state + config 的组合
const runInput = {
  githubIssueId: 123,
  targetRepository: { owner: "acme", repo: "demo", branch: "feat/login" },
  taskPlan: null,                   // 首次为空，后续跟随 Issue 更新
  branchName: "feat/login-abc123", // 若 state.branchName 空，则由 getBranchName 生成
  autoAcceptPlan: false,            // 用户需手动接受/编辑
  messages: [/* 跟进消息（跟随用户追加） */],
};
```

Planner → proposed plan（前端交互）：
```ts
// 前端展示 state.proposedPlanTitle 与 state.proposedPlan (string[])
// 用户点击“接受”：humanResponse.type = 'accept'
// 用户点击“编辑”：humanResponse.type = 'edit' 且 args.plan 为按分隔符拼接的文本
```

编辑计划的用户输入格式（带分隔符）：
```text
步骤1：阅读现有登录代码
---PLAN_ITEM---
步骤2：实现登录 API 与 OAuth 支持
---PLAN_ITEM---
步骤3：补充测试与错误处理
```

### 6. 小结

- Web 输入 → classifyMessage：决定是否创建 Issue、如何路由（含补写评论与恢复 Planner）；
- start-planner：启动 Planner 生成 `proposedPlanTitle`/`proposedPlan`；
- interruptProposedPlan：把候选方案写回 Issue 并等待用户操作；
- 用户接受/编辑：转换为 `taskPlan`，写回 Issue，启动 Programmer；
- 通过 Issue 标签段实现上下文持久化，可随时恢复现状。


