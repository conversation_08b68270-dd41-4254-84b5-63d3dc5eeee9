### Open SWE 子图总览：跨子图时序与状态转移矩阵

本篇汇总 Manager、Planner、Programmer、Reviewer 四个子图的跨图交互；给出端到端时序图、关键状态在各阶段的转移矩阵与主要分支路径，便于全局理解与调试。

---

## 1. 端到端时序图（核心 happy path + 常见分支）

```mermaid
sequenceDiagram
    participant U as User
    participant M as Manager
    participant Pn as Planner
    participant Pg as Programmer
    participant Rv as Reviewer
    participant GH as GitHub
    participant SB as Sandbox

    U->>M: 发送初始请求（或已有 GitHub Issue）
    M->>GH: （非本地）创建/读取 Issue、补写评论
    M->>Pn: start-planner（含任务计划/跟进消息）
    Note over Pn,SB: initialize-sandbox（克隆/恢复、本地或沙箱）
    loop 只读收集循环
      Pn->>SB: grep/view/shell/search_document_for...（只读）
      SB-->>Pn: 上下文结果、codebaseTree 更新
    end
    Pn->>Pn: generate-plan（session_plan）
    Pn->>Pn: notetaker（write_technical_notes）
    Pn-->>U: interrupt-proposed-plan（人工审核/自动接受）
    alt 用户接受/编辑/auto-accept
      Pn->>Pg: 启动 Programmer（taskPlan）
      Note over Pg,SB: initialize（复用/新建沙箱）
      loop 执行循环
        Pg->>SB: apply_patch/text_editor/shell/grep/install...
        SB-->>Pg: 执行结果、codebaseTree 更新
        alt 需要审查
          Pg->>Rv: reviewer-subgraph（只读审查）
          loop 审查收集循环
            Rv->>SB: grep/view/shell/scratchpad（只读）
            SB-->>Rv: 结果
          end
          alt 审查通过
            Rv-->>Pg: generate-conclusion
          else 审查不通过
            Rv-->>Pg: 更新 taskPlan 的 additional_actions
          end
        end
      end
      Pg->>GH: （非本地）open-pr / 更新 PR 为 ready
      Pg-->>U: 结论与 PR 链接
    else 用户 response（评论/跟进）
      Pn->>Pn: determine-needs-context
      alt need_context
        Pn->>SB: 继续只读收集
      else have_context
        Pn->>Pn: 直接 generate-plan（修订计划）
      end
    end

    Note over U,M: 后续任意时间用户跟进 → Manager classify → 路由至 Planner 跟进或创建新会话
```

---

## 2. 关键状态转移矩阵（跨子图）

| 状态/阶段 | Manager | Planner | Programmer | Reviewer |
| --- | --- | --- | --- | --- |
| messages | 入口/同步 Issue 评论 | 只读动作/计划/笔记/中断消息 | 执行期工具输出和结论 | 审查期工具输出与结论 |
| internalMessages | - | - | 执行内部上下文与路由依据 | 引用 Programmer 内部历史，审查内部历史 |
| reviewerMessages | - | - | - | 审查内部对话（计数上限） |
| sandboxSessionId | - | initialize-sandbox 设定 | initialize/动作中复用与管理 | initialize-state 复用 |
| targetRepository/branchName | 传入/生成 | 传递与复用 | 执行与提交分支 | 审查 diff 基准与分支 |
| codebaseTree | - | 生成/更新 | 每次动作后尝试更新 | 初始化与动作后更新 |
| githubIssueId | 创建/同步 | 读取评论/写回计划与笔记 | 写回任务总结/PR 前后评论 | 写回审查后的新增动作（如不通过） |
| taskPlan | 从 Issue 提取（可覆盖） | 输入为上下文；未变更 | 完成项/总结/PR 号等持续更新 | 通过则不变，不通过合入 additional_actions |
| proposedPlan(Title) | - | generate-plan 产出 | - | - |
| contextGatheringNotes | - | notetaker 产出 | 作为执行期动态提示输入 | - |
| plannerSession/programmerSession | 管理 Planner session | 可启动 Programmer | 执行期产生/更新 | - |
| reviewsCount | - | - | 决定路由至结论或继续审查 | 审查结束时递增 |
| dependenciesInstalled | - | 只读阶段记录（可能为 null） | 执行期安装并跟踪 | 只读阶段记录并合并 |
| customRules | - | initialize-sandbox 注入 | 执行期提示 | 审查提示 |
| tokenData | - | 记录模型调用 token | 同步记录 | 同步记录 |
| autoAcceptPlan | 传入 Planner | 影响是否中断人工审核 | - | - |

注：表格描述的是主通路，细节以子图文档为准；本地模式下跳过 GitHub 交互与部分鉴权逻辑。

---

## 3. 主要分支与回路

- 跟进评论（用户 response）：Planner 的 `interrupt-proposed-plan → determine-needs-context` 回路决定继续只读收集或直接修订计划。
- 审查回路：Programmer 在需要质量保证时进入 Reviewer；通过则生成结论，不通过则把 `additional_actions` 合并到 `taskPlan`，Programmer 继续执行。
- 只读/执行超限：Planner 的 `maxContextActions*2` 和 Reviewer 的 `maxReviewActions*2` 达阈值分别进入生成计划或最终审查；Programmer 的“最近两条无 tool_calls”守卫避免空转。
- Manager 路由：`respond_and_route` 根据 Planner/Programmer 运行态与 Issue 上下文，选择新会话、启动/恢复 Planner 或直接结束。

---

## 4. 常见失败点与定位建议

- 计划与 Issue 不一致：优先核对 Manager/Planner 在进入时是否从 Issue 正确提取 `taskPlan` 与评论。
- Planner 循环过长：检查 `maxContextActions` 与工具是否产生有效上下文；必要时手动接受计划或精炼需求。
- Programmer 无法产生工具调用：核对提示上下文（计划项、notes、代码树、依赖状态）是否齐备；关注“最近两条无 tool_calls”守卫。
- Reviewer 一直不收敛：确认 `changedFiles` 的基准分支是否正确，并检查是否一直处于只读动作收集未达“done”信号。

---

## 5. 快速检核（端到端）

1) Manager 是否创建/同步 Issue；Planner 是否启动成功且拿到 `sandboxSessionId`
2) Planner 是否产出 `proposedPlan(Title)` 与 `contextGatheringNotes`
3) Programmer 是否在任务完成时更新 `taskPlan` 并产出结论；非本地模式是否打开/更新 PR
4) Reviewer 若参与，是否在不通过时把新增动作写回并递增 `reviewsCount`

---

以上为跨子图的端到端视角，建议配合各子图文档与运行日志、Issue/PR 痕迹共同排查。


