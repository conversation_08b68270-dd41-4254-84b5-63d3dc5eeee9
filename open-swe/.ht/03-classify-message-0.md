## classifyMessage 分析

### 已完成
- 我已通读 `classifyMessage` 全部实现与相关工具/Schema，完成逻辑梳理与全场景示例，并写入到 `/.ht/01-classify-message.md`。

### 关键逻辑一图流
- 状态读取：获取最新 Human 消息；读取 Planner/Programmer 线程状态与 `taskPlan`、`proposedPlan`、`requestSource`。
- 动态构造路由选项与提示词：依据线程状态拼装可选 `route`（如 `start_planner`、`update_planner`、`update_programmer`、`resume_and_update_planner`、`start_planner_for_followup`、`create_new_issue`、`no_op`）并生成 Schema。
- 模型调用固定工具 `respond_and_route`：强制输出结构化的 `route/response`。
- 路由处理：
  - Local 模式：仅支持 `start_planner` / `start_planner_for_followup`。
  - 非 Local 模式：保证 GitHub Issue 存在；把未入 Issue 的 Human 消息补齐为评论并在消息 `additional_kwargs` 中打标（`githubIssueId`、`githubIssueCommentId`、`isOriginalIssue`、`isFollowup`）；必要时恢复被中断的 Planner；根据路由跳转或结束。

### 各路由何时出现（简表）
- programmer=busy → 显示 `update_programmer`
- planner=not_started → 显示 `start_planner`
- planner=idle 且 programmer=idle → 显示 `start_planner_for_followup`
- planner=busy → 显示 `update_planner`
- planner=interrupted → 显示 `resume_and_update_planner`
- 任一方≠not_started → 显示 `create_new_issue`
- 永远可选 → `no_op`

### 场景化示例（精简）
- A 新对话（两者 not_started）：用户“添加健康检查接口” → `start_planner`；Local 直跳 Planner；非 Local 先建 Issue 再跳。
- B Planner 运行中（planner=busy）：用户补充需求 → `update_planner`；写 Issue 评论，结束。
- C Programmer 运行中（programmer=busy）：用户“把日志级别改为 debug” → `update_programmer`；写评论，结束。
- D Planner 被中断（planner=interrupted）：用户“同意方案，继续” → `resume_and_update_planner`；恢复 Planner（更新 runId），结束。
- E 双 idle（planner=idle, programmer=idle）：用户“再做一个 /metrics 端点” → `start_planner_for_followup`；（如需）补评论并标注 `isFollowup=true`，再跳 Planner。
- F 新会话：用户“另开一个 issue 处理 CI 缓存” → `create_new_issue`；进入 `create-new-session`。
- G 闲聊/致谢：用户“谢谢” → `no_op`；仅回复并结束。

### 详细文档
- 请查看 `/.ht/01-classify-message.md`（已包含代码片段引用、状态判定、各分支行为与完整示例）。

- 做了哪些事
  - 在 `/.ht/01-classify-message.md` 输出了关于 `classifyMessage` 的结构化说明与多场景示例，便于后续查阅与追踪。
