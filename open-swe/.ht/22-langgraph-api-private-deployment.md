# LangGraph API 私有化部署指南

## 概述

本文档提供不使用 `langgraph-cli` 的 LangGraph API 私有化部署方案，支持与常用 Node.js 服务框架集成。

## 部署方案对比

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| langgraph-cli | 简单快速，开箱即用 | 功能受限，难以定制 | 开发测试 |
| 直接集成 | 完全控制，高度定制 | 需要更多配置 | 生产环境 |
| 容器化部署 | 环境一致，易于扩展 | 资源开销 | 云原生环境 |

## 方案一：直接集成部署

### 1. 项目结构

```
my-langgraph-app/
├── package.json
├── src/
│   ├── graphs/
│   │   ├── chat-graph.ts
│   │   └── workflow-graph.ts
│   ├── server.ts
│   └── config.ts
├── langgraph.json
└── .env
```

### 2. 依赖安装

```bash
npm install @langchain/langgraph-api @langchain/langgraph @langchain/core
npm install express cors helmet compression
npm install -D typescript @types/node
```

### 3. 核心集成代码

#### 3.1 服务器启动文件 (src/server.ts)

```typescript
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { startServer, StartServerSchema } from '@langchain/langgraph-api';
import path from 'path';

const app = express();

// 中间件配置
app.use(helmet());
app.use(compression());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true
}));

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// LangGraph API 配置
const langgraphConfig: StartServerSchema = {
  port: parseInt(process.env.LANGGRAPH_PORT || '8123'),
  nWorkers: parseInt(process.env.LANGGRAPH_WORKERS || '2'),
  host: process.env.LANGGRAPH_HOST || '0.0.0.0',
  cwd: process.cwd(),
  graphs: {
    'chat-assistant': './src/graphs/chat-graph.ts:default',
    'workflow-assistant': './src/graphs/workflow-graph.ts:default'
  },
  auth: {
    path: './src/auth/custom-auth.ts',
    disable_studio_auth: false
  },
  ui: {
    'studio': '@langchain/langgraph-ui'
  },
  ui_config: {
    shared: ['chat-assistant', 'workflow-assistant']
  },
  http: {
    cors: {
      allow_origins: ['http://localhost:3000'],
      allow_methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      allow_headers: ['Content-Type', 'Authorization'],
      allow_credentials: true
    }
  }
};

// 启动 LangGraph API 服务
async function startLangGraphAPI() {
  try {
    const { host, cleanup } = await startServer(langgraphConfig);
    console.log(`🚀 LangGraph API 服务已启动: http://${host}`);
    
    // 优雅关闭
    process.on('SIGTERM', async () => {
      console.log('正在关闭 LangGraph API 服务...');
      await cleanup();
      process.exit(0);
    });
    
    return { host, cleanup };
  } catch (error) {
    console.error('启动 LangGraph API 服务失败:', error);
    throw error;
  }
}

// 启动服务
async function main() {
  const langgraph = await startLangGraphAPI();
  
  // 可以在这里添加其他服务逻辑
  console.log('✅ 所有服务已启动');
}

main().catch(console.error);
```

#### 3.2 图定义文件 (src/graphs/chat-graph.ts)

```typescript
import { StateGraph, END } from '@langchain/langgraph';
import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage, AIMessage } from '@langchain/core/messages';

// 状态定义
interface ChatState {
  messages: (HumanMessage | AIMessage)[];
  user_id?: string;
  session_id?: string;
}

// 创建聊天图
export function createChatGraph() {
  const llm = new ChatOpenAI({
    modelName: 'gpt-3.5-turbo',
    temperature: 0.7,
    openAIApiKey: process.env.OPENAI_API_KEY
  });

  const workflow = new StateGraph<ChatState>({
    channels: {
      messages: {
        reducer: (current: (HumanMessage | AIMessage)[], update: (HumanMessage | AIMessage)[]) => {
          return [...current, ...update];
        }
      }
    }
  });

  // 添加节点
  workflow.addNode('chat', async (state: ChatState) => {
    const response = await llm.invoke(state.messages);
    return { messages: [response] };
  });

  // 设置入口和出口
  workflow.setEntryPoint('chat');
  workflow.addEdge('chat', END);

  return workflow.compile();
}

export default createChatGraph;
```

#### 3.3 配置文件 (src/config.ts)

```typescript
export const config = {
  langgraph: {
    port: parseInt(process.env.LANGGRAPH_PORT || '8123'),
    host: process.env.LANGGRAPH_HOST || '0.0.0.0',
    workers: parseInt(process.env.LANGGRAPH_WORKERS || '2'),
    storage: {
      type: 'filesystem',
      path: './data/langgraph'
    }
  },
  auth: {
    enabled: process.env.AUTH_ENABLED === 'true',
    type: process.env.AUTH_TYPE || 'jwt',
    secret: process.env.JWT_SECRET || 'your-secret-key'
  },
  cors: {
    origins: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
    credentials: true
  }
};
```

### 4. 环境配置 (.env)

```bash
# LangGraph 配置
LANGGRAPH_PORT=8123
LANGGRAPH_HOST=0.0.0.0
LANGGRAPH_WORKERS=2
LANGGRAPH_API_URL=http://localhost:8123

# 认证配置
AUTH_ENABLED=true
AUTH_TYPE=jwt
JWT_SECRET=your-super-secret-key-here

# CORS 配置
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# OpenAI 配置
OPENAI_API_KEY=your-openai-api-key

# 存储配置
STORAGE_PATH=./data/langgraph
```

## 方案二：Express.js 集成部署

### 1. Express 中间件集成

```typescript
import express from 'express';
import { startServer } from '@langchain/langgraph-api';

const app = express();

// 将 LangGraph API 作为 Express 中间件
app.use('/api/langgraph', async (req, res, next) => {
  const langgraphApp = await startServer({
    port: 0, // 使用随机端口
    nWorkers: 2,
    host: '127.0.0.1',
    cwd: process.cwd(),
    graphs: {
      'chat': './src/graphs/chat-graph.ts:default'
    }
  });
  
  // 代理请求到 LangGraph API
  req.url = req.url.replace('/api/langgraph', '');
  return langgraphApp.fetch(req, res);
});

app.listen(3000, () => {
  console.log('Express 服务器运行在端口 3000');
});
```

## 方案三：Fastify 集成部署

### 1. Fastify 插件集成

```typescript
import Fastify from 'fastify';
import { startServer } from '@langgraph/langgraph-api';

const fastify = Fastify({ logger: true });

// 注册 LangGraph 插件
fastify.register(async (fastify) => {
  const langgraphApp = await startServer({
    port: 0,
    nWorkers: 2,
    host: '127.0.0.1',
    cwd: process.cwd(),
    graphs: {
      'chat': './src/graphs/chat-graph.ts:default'
    }
  });

  // 代理所有 /langgraph 请求
  fastify.all('/langgraph/*', async (request, reply) => {
    const url = new URL(request.url, 'http://localhost');
    url.pathname = url.pathname.replace('/langgraph', '');
    
    const response = await langgraphApp.fetch(url.toString(), {
      method: request.method,
      headers: request.headers as Record<string, string>,
      body: request.body
    });
    
    reply.status(response.status);
    reply.headers(response.headers as Record<string, string>);
    return response.body;
  });
});

fastify.listen({ port: 3000 }, (err) => {
  if (err) throw err;
});
```

## 方案四：Docker 容器化部署

### 1. Dockerfile

```dockerfile
FROM node:18-alpine

WORKDIR /app

# 安装依赖
COPY package*.json ./
RUN npm ci --only=production

# 复制应用代码
COPY . .

# 创建数据目录
RUN mkdir -p /app/data/langgraph

# 暴露端口
EXPOSE 8123

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8123/health || exit 1

# 启动命令
CMD ["node", "dist/server.js"]
```

### 2. Docker Compose

```yaml
version: '3.8'

services:
  langgraph-api:
    build: .
    ports:
      - "8123:8123"
    environment:
      - NODE_ENV=production
      - LANGGRAPH_PORT=8123
      - LANGGRAPH_HOST=0.0.0.0
      - LANGGRAPH_WORKERS=4
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - langgraph-data:/app/data/langgraph
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8123/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - langgraph-api
    restart: unless-stopped

volumes:
  langgraph-data:
```

### 3. Nginx 配置

```nginx
events {
    worker_connections 1024;
}

http {
    upstream langgraph_backend {
        server langgraph-api:8123;
    }

    server {
        listen 80;
        server_name your-domain.com;

        location / {
            proxy_pass http://langgraph_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket 支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
```

## 方案五：Kubernetes 部署

### 1. Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: langgraph-api
  labels:
    app: langgraph-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: langgraph-api
  template:
    metadata:
      labels:
        app: langgraph-api
    spec:
      containers:
      - name: langgraph-api
        image: your-registry/langgraph-api:latest
        ports:
        - containerPort: 8123
        env:
        - name: NODE_ENV
          value: "production"
        - name: LANGGRAPH_PORT
          value: "8123"
        - name: LANGGRAPH_HOST
          value: "0.0.0.0"
        - name: LANGGRAPH_WORKERS
          value: "2"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: langgraph-secrets
              key: openai-api-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8123
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8123
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: langgraph-data
          mountPath: /app/data/langgraph
      volumes:
      - name: langgraph-data
        persistentVolumeClaim:
          claimName: langgraph-pvc
```

### 2. Service

```yaml
apiVersion: v1
kind: Service
metadata:
  name: langgraph-service
spec:
  selector:
    app: langgraph-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8123
  type: ClusterIP
```

### 3. Ingress

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: langgraph-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: api.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: langgraph-service
            port:
              number: 80
```

## 部署步骤

### 1. 准备工作

```bash
# 1. 创建项目目录
mkdir my-langgraph-app && cd my-langgraph-app

# 2. 初始化项目
npm init -y

# 3. 安装依赖
npm install @langchain/langgraph-api @langchain/langgraph @langchain/core
npm install express cors helmet compression
npm install -D typescript @types/node

# 4. 创建 TypeScript 配置
npx tsc --init
```

### 2. 配置图定义

```bash
# 创建图定义目录
mkdir -p src/graphs

# 创建图定义文件
touch src/graphs/chat-graph.ts
touch src/graphs/workflow-graph.ts
```

### 3. 环境配置

```bash
# 创建环境文件
cp .env.example .env

# 编辑环境变量
vim .env
```

### 4. 构建和启动

```bash
# 构建项目
npm run build

# 启动服务
npm start
```

### 5. 验证部署

```bash
# 健康检查
curl http://localhost:8123/health

# 创建助手
curl -X POST http://localhost:8123/assistants \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Chat Assistant",
    "graph_id": "chat-assistant",
    "config": {}
  }'
```

## 性能优化建议

### 1. 内存优化

```typescript
// 配置 Node.js 内存限制
process.env.NODE_OPTIONS = '--max-old-space-size=2048';

// 启用垃圾回收
setInterval(() => {
  if (global.gc) {
    global.gc();
  }
}, 30000);
```

### 2. 连接池优化

```typescript
// 数据库连接池配置
const poolConfig = {
  max: 20,
  min: 5,
  acquire: 30000,
  idle: 10000
};
```

### 3. 缓存策略

```typescript
// Redis 缓存集成
import Redis from 'ioredis';

const redis = new Redis({
  host: process.env.REDIS_HOST,
  port: parseInt(process.env.REDIS_PORT || '6379'),
  maxRetriesPerRequest: 3
});

// 缓存图定义
const cachedGraph = await redis.get(`graph:${graphId}`);
if (cachedGraph) {
  return JSON.parse(cachedGraph);
}
```

## 监控和日志

### 1. 日志配置

```typescript
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

### 2. 指标监控

```typescript
import prometheus from 'prom-client';

// 创建指标
const httpRequestDuration = new prometheus.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code']
});

// 中间件
app.use((req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    httpRequestDuration
      .labels(req.method, req.route?.path || req.path, res.statusCode.toString())
      .observe(duration);
  });
  next();
});
```

## 安全配置

### 1. 认证中间件

```typescript
import jwt from 'jsonwebtoken';

const authMiddleware = (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    return res.status(401).json({ error: 'No token provided' });
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({ error: 'Invalid token' });
  }
};

app.use('/api', authMiddleware);
```

### 2. 速率限制

```typescript
import rateLimit from 'express-rate-limit';

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: 'Too many requests from this IP'
});

app.use('/api', limiter);
```

## 故障排除

### 1. 常见问题

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 图加载失败 | 文件路径错误 | 检查图定义文件路径和导出 |
| 内存泄漏 | 长时间运行 | 增加垃圾回收频率 |
| 连接超时 | 网络问题 | 调整超时配置 |
| 权限错误 | 文件权限 | 检查数据目录权限 |

### 2. 调试命令

```bash
# 检查端口占用
netstat -tulpn | grep 8123

# 查看日志
tail -f logs/app.log

# 内存使用情况
ps aux | grep node

# 网络连接
ss -tulpn | grep 8123
```

## 总结

通过以上方案，您可以灵活地将 LangGraph API 集成到现有的 Node.js 应用中，实现私有化部署。推荐根据您的具体需求选择合适的方案：

- **开发环境**: 直接集成方案
- **生产环境**: Docker 容器化方案
- **大规模部署**: Kubernetes 方案
- **现有应用集成**: Express/Fastify 中间件方案

每种方案都提供了完整的配置示例和最佳实践，确保您的 LangGraph API 服务能够稳定、高效地运行。
