### Open SWE Programmer 子图 逻辑流转与状态变更全解析

本篇延续 Planner 文档风格，系统梳理 Programmer 子图的控制流、节点职责、状态变量、关键路由条件与异常路径；附 Mermaid 可视化与典型场景，便于理解执行期的自动编码与交互流程。

---

## 1. Programmer 的目标与产出

- 目标: 基于 Planner 生成的 `taskPlan`，在沙箱/本地环境内执行真实的编辑与命令操作，直至任务完成；期间支持请求人工帮助、计划更新、错误诊断、历史摘要与多轮审查；完成后生成结论并（非本地模式）打开/更新 Pull Request。
- 主要产出:
  - 对话与内部消息（`messages`/`internalMessages`）中的工具输出、总结、诊断与审查结果
  - 持续更新的 `taskPlan`（完成状态与总结）
  - PR 的创建/更新与就绪标记（非本地模式）

---

## 2. Programmer 的状态（GraphState）要点

- `messages`：用户可见消息（含工具消息）
- `internalMessages`：执行内部对话（用于上文与工具路由；可能包含更多历史与控制信息）
- `sandboxSessionId`：沙箱会话
- `targetRepository`、`branchName`：目标仓库与分支
- `codebaseTree`：代码树（每次动作后尝试更新，失败则回退上次）
- `taskPlan`：当前执行计划（含完成项 summary、当前项、剩余项）
- `dependenciesInstalled`：依赖安装状态（用于动态提示）
- `customRules`：自定义规则（影响执行提示与 PR 格式）
- `reviewsCount`：已触发审查次数（用于上限判断）

---

## 3. 控制流（高层）

```mermaid
graph TD
  START((START)) --> A[initialize]
  A --> B[generate-action]
  B -->|AI含tool_calls且name=request_human_help| RH[request-help]
  B -->|AI含tool_calls且name=update_plan含reasoning| UP[update-plan]
  B -->|AI含tool_calls且name=mark_task_completed| HC[handle-completed-task]
  B -->|AI含其他tool_calls| T[take-action]
  B -->|AI无tool_calls且有剩余任务且最近2条并非都无tool_calls| B
  B -->|AI无tool_calls| RRC[route-to-review-or-conclusion]
  RH -->|response| B
  RH -->|ignore| END
  UP --> B
  HC -->|需总结| SH[summarize-history]
  HC -->|有剩余| B
  HC -->|无剩余| RRC
  SH --> B
  T -->|错误| DE[diagnose-error]
  T -->|继续| B
  DE --> B
  RRC -->|reviewsCount>=max| GC[generate-conclusion]
  RRC -->|否则| RV[reviewer-subgraph]
  RV -->|全部完成| GC
  RV -->|仍有任务| B
  GC -->|本地| END
  GC -->|沙箱| OP[open-pr]
  OP --> END
```

对照源码：

```130:174:apps/open-swe/src/graphs/programmer/index.ts
const workflow = new StateGraph(GraphAnnotation, GraphConfiguration)
  .addNode("initialize", initializeSandbox)
  .addNode("generate-action", generateAction)
  .addNode("take-action", takeAction, { ends: ["generate-action", "diagnose-error"] })
  .addNode("update-plan", updatePlan)
  .addNode("handle-completed-task", handleCompletedTask, { ends: ["summarize-history", "generate-action", "route-to-review-or-conclusion"] })
  .addNode("generate-conclusion", generateConclusion, { ends: ["open-pr", END] })
  .addNode("request-help", requestHelp, { ends: ["generate-action", END] })
  .addNode("route-to-review-or-conclusion", routeToReviewOrConclusion, { ends: ["generate-conclusion", "reviewer-subgraph"] })
  .addNode("reviewer-subgraph", reviewerGraph)
  .addNode("open-pr", openPullRequest)
  .addNode("diagnose-error", diagnoseError)
  .addNode("summarize-history", summarizeHistory)
  .addEdge(START, "initialize")
  .addEdge("initialize", "generate-action")
  .addConditionalEdges("generate-action", routeGeneratedAction, ["take-action","request-help","route-to-review-or-conclusion","update-plan","generate-action","handle-completed-task"]) 
  .addEdge("update-plan", "generate-action")
  .addEdge("diagnose-error", "generate-action")
  .addConditionalEdges("reviewer-subgraph", routeGenerateActionsOrEnd, ["generate-conclusion","generate-action"])
  .addEdge("summarize-history", "generate-action")
  .addEdge("open-pr", END)
```

---

## 4. 关键节点与状态变更

### 4.1 initialize
- 复用与 Planner 相同的 `initialize-sandbox`：恢复/创建沙箱、更新 `codebaseTree`、解析 `customRules`，并以事件消息写入 `messages`。
- 转移：`generate-action`。

### 4.2 generate-action（生成执行动作）
- 绑定工具（按提供商差异注入）：
  - 通用：`grep`、`shell`、`request_human_help`、`update_plan`、`install_dependencies`、`mark_task_completed`、`search_document_for`、`write_default_tsconfig`、`get_url_content`、MCP 工具
  - Anthropic 专属：`str_replace_based_edit_tool`（内建编辑器）
  - 非 Anthropic：`apply_patch`（统一编辑入口，标记 cache_control）
- 提示结构：静态指令 + 动态上下文（计划、notes、目录与树、依赖装态）+ 可选代码审查上下文
- 若无 `tool_calls`：停止沙箱（若存在），进入路由到审查/结论
- 更新：`messages`、`internalMessages`、`sandboxSessionId?`、`taskPlan?`、`tokenData`

### 4.3 routeGeneratedAction（动作路由）
- 若 `request_human_help` → `request-help`
- 若 `update_plan`（且含 `update_plan_reasoning`）→ `update-plan`（以 Send 形式，写入 `planChangeRequest`）
- 若 `mark_task_completed` → `handle-completed-task`
- 若有其他工具 → `take-action`
- 若无工具：
  - 若有剩余任务，且“最近两条并非都无 tool_calls” → 回到 `generate-action`
  - 否则 → `route-to-review-or-conclusion`

### 4.4 take-action（执行工具调用）
- 工具集合：`apply_patch`、`text_editor`、`shell`、`install_dependencies`、`grep`、`write_default_tsconfig`、`get_url_content`、`search_document_for`、MCP 工具
- 本地模式下会对命令进行安全过滤（`filterUnsafeCommands`），并将过滤后的 `AIMessage` 注入 `internalMessages`
- 每次动作后：提取并合并 `documentCache` 更新；若存在文件改动则在沙箱模式下提交分支并可能关联 PR 号；更新 `codebaseTree`；必要时将 PR 提示消息加入用户可见 `messages`
- 路由：若需错误诊断 → `diagnose-error`；否则回到 `generate-action`

### 4.5 diagnose-error（错误诊断）
- 基于最近失败的工具输出、当前任务、已完成任务与代码树，使用 Summarizer 调用 `diagnose_error` 工具生成诊断说明
- 更新：`messages` 与 `internalMessages` 附加诊断结果
- 路由：回到 `generate-action`

### 4.6 handle-completed-task（完成当前任务）
- 要求上一条 `AIMessage` 含 `mark_task_completed` 工具调用；构建 `ToolMessage` 确认
- 更新 `taskPlan` 完成状态并写回 GitHub Issue（非本地模式）
- 若无剩余任务 → `route-to-review-or-conclusion`
- 若内部消息 token 超限 → `summarize-history`（见 4.7）
- 否则 → `generate-action`

### 4.7 summarize-history（历史摘要压缩）
- 检索自上次摘要以来的消息（排除隐藏，保留尾部 20 条）并用 Summarizer 生成压缩摘要
- 用 `RemoveMessage` 移除旧消息并注入“摘要的 AI+ToolMessage 假工具对”
- 仅更新会话，不改变任务流向 → 回到 `generate-action`

### 4.8 update-plan（基于 reasoning 更新计划）
- 从上一条 `AIMessage` 读取 `update_plan_reasoning`，再用 Programmer LLM 调用 `update_plan` 工具输出新的剩余计划（已完成项保留且不可变）
- 更新 `taskPlan` 并写回 GitHub Issue
- 将裁剪后的“仅含 reasoning 工具调用”的 AIMessage 与确认 ToolMessage 写回 `messages/internalMessages`
- 路由：回到 `generate-action`

### 4.9 request-help（请求人工帮助）
- 停止沙箱；在 GitHub Issue 留言@所有者并附链接；中断等待人工回复
- `ignore` → END；`response` → 恢复沙箱并将回复注入，回到 `generate-action`

### 4.10 route-to-review-or-conclusion 与 reviewer-subgraph
- 若 `reviewsCount` 达到上限（默认 3）→ `generate-conclusion`
- 否则进入 Reviewer 子图进行代码审查与动作建议；审查返回：若所有任务已完成 → `generate-conclusion`，否则 → `generate-action`

### 4.11 generate-conclusion（结论产出）
- 使用 Summarizer 根据已完成任务与对话历史生成简明结论，更新 `taskPlan` 的最后任务 summary 并写回 GitHub Issue（非本地模式）
- 本地模式 → 直接 END；沙箱模式 → `open-pr`

### 4.12 open-pr（打开/更新 PR 并清理沙箱）
- 如有改动则提交；使用 Router 调用 `open_pr` 工具生成 PR 标题与正文，若已有 PR 则更新为 ready for review
- 删除沙箱、重置依赖标记；把 PR 链接消息写回 `messages/internalMessages`
- END

---

## 5. 关键路由与守卫条件

- `routeGeneratedAction`：
  - `request_human_help` → 请求帮助
  - `update_plan`（含 reasoning）→ 更新计划
  - `mark_task_completed` → 标记完成
  - 否则若有工具 → 执行；无工具：视剩余任务与最近两条消息是否“都无工具”来决定回退生成或进入审查/结论

- `routeGenerateActionsOrEnd`（审查后）：
  - 所有活动任务已完成 → `generate-conclusion`
  - 否则 → `generate-action`

- PR 打开前的提交：仅沙箱模式在检测到改动时执行 `checkoutBranchAndCommit`，并可能更新 `taskPlan` 的 PR 号

---

## 6. 典型场景与状态变化轨迹

### 场景 A：常规执行直至完成并开 PR
1) `initialize` → 2) 多轮 `generate-action ↔ take-action`（期间可能 `diagnose-error`）→ 3) `handle-completed-task`（若完成）→ 4) `route-to-review-or-conclusion` → Reviewer 判定全部完成 → 5) `generate-conclusion` → 6) `open-pr` → END

关键状态：
- `internalMessages` 持续追加工具调用结果与中间总结；`codebaseTree` 与 `dependenciesInstalled` 动态更新
- `taskPlan` 完成项与 summary 更新，并写回 GitHub

### 场景 B：请求人工帮助
- `generate-action` 生成 `request_human_help` → `request-help` 停止沙箱并发起中断 → 用户 `response` → 恢复沙箱，将回复注入并继续 `generate-action`

### 场景 C：执行中更新计划
- `generate-action` 认为计划需变更，给出 `update_plan_reasoning` → `update-plan` 调用计划更新工具，产出新的剩余计划 → 回到 `generate-action` 继续

### 场景 D：对话过长触发历史摘要
- `handle-completed-task` 检测到内部消息 token 超限 → `summarize-history` 压缩历史 → 继续执行

---

## 7. 工具与模型任务分工

- 模型：
  - `LLMTask.PROGRAMMER`：`generate-action`、`update-plan`
  - `LLMTask.SUMMARIZER`：`diagnose-error`、`generate-conclusion`、`summarize-history`
  - `LLMTask.ROUTER`：`open-pr`

- 关键工具：
  - 编辑与补丁：`apply_patch` 或 Anthropic 的 `text_editor_20250429`
  - 运行/安装：`shell`、`install_dependencies`
  - 计划与状态：`update_plan`、`mark_task_completed`
  - 代码检索/查看：`grep`、`search_document_for`、`get_url_content`
  - 其它：`write_default_tsconfig`、MCP 工具族

---

## 8. 调试与排障建议

- 若动作后无 PR：检查是否在沙箱模式且存在改动；留意 `checkoutBranchAndCommit` 的日志
- 若频繁无工具调用：关注 `routeGeneratedAction` 的“最近两条无工具”守卫是否阻断；检查提示是否足够具体
- 若依赖相关命令失败：确认已先执行 `install_dependencies`；工具调用中有依赖安装的状态回传
- 若历史过长：查看 `summarize-history` 是否触发；必要时调高 `MAX_INTERNAL_TOKENS`
- 本地 vs 沙箱：本地模式会过滤潜在危险命令；PR 创建流程仅在沙箱模式进行

---

以上为 Programmer 的全流程与状态说明。建议结合 `messages/internalMessages` 与 GitHub 侧痕迹（Issue 评论、PR 变更）进行定位与追踪。


