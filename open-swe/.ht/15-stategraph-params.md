# StateGraph 参数和配置的对应关系

## 分析

## `streaming.ts` 中 `newClient.runs.create` 的参数与 `index.ts` 中 graph 状态和配置的对应关系

### 1. **Graph 状态 (ManagerGraphStateObj)**

在 `index.ts` 中定义的 `ManagerGraphStateObj` 包含以下状态字段：

```typescript
// ManagerGraphStateObj 包含的状态字段：
- githubIssueId: number
- githubPullRequestId: number (可选)
- targetRepository: TargetRepository
- taskPlan: TaskPlan
- programmerSession: AgentSession (可选)
- plannerSession: AgentSession (可选)
- branchName: string
- autoAcceptPlan: boolean (可选)
- messages: Messages (继承自 MessagesZodState)
```

### 2. **Graph 配置 (GraphConfiguration)**

在 `index.ts` 中定义的 `GraphConfiguration` 包含以下配置字段：

```typescript
// GraphConfiguration 包含的配置字段：
- thread_id: string (可选)
- run_id: string (可选)
- maxContextActions: number (可选，默认75)
- maxReviewActions: number (可选，默认30)
- plannerModelName: string (可选，默认"anthropic:claude-sonnet-4-0")
- plannerTemperature: number (可选，默认0)
- programmerModelName: string (可选，默认"anthropic:claude-sonnet-4-0")
- programmerTemperature: number (可选，默认0)
- reviewerModelName: string (可选，默认"anthropic:claude-sonnet-4-0")
- reviewerTemperature: number (可选，默认0)
- routerModelName: string (可选，默认"anthropic:claude-3-5-haiku-latest")
- routerTemperature: number (可选，默认0)
- summarizerModelName: string (可选，默认"anthropic:claude-sonnet-4-0")
- summarizerTemperature: number (可选，默认0)
- maxTokens: number (可选，默认10000)
- mcpServers: string (可选)
- apiKeys: object (可选)
- 各种 GitHub 相关配置
```

### 3. **参数对应关系**

在 `streaming.ts` 第187行的 `newClient.runs.create` 调用中：

```typescript
const run = await newClient.runs.create(threadId, MANAGER_GRAPH_ID, {
  input: runInput,  // 对应 Graph 状态的初始值
  config: {         // 对应 GraphConfiguration
    recursion_limit: 400,
  },
  ifNotExists: "create",
  streamResumable: true,
  streamMode: OPEN_SWE_STREAM_MODE as StreamMode[],
});
```

**具体对应关系：**

1. **`input: runInput`** - 对应 `ManagerGraphStateObj` 的初始状态：
   ```typescript
   const runInput = {
     messages: [...],           // 对应 messages 状态
     targetRepository: {...},   // 对应 targetRepository 状态
     autoAcceptPlan: false,     // 对应 autoAcceptPlan 状态
   };
   ```

2. **`config: {...}`** - 对应 `GraphConfiguration` 的配置：
   ```typescript
   config: {
     recursion_limit: 400,  // 这是 LangGraph 的通用配置，不是 GraphConfiguration 中的字段
     // 其他 GraphConfiguration 字段会使用默认值或从其他地方传入
   }
   ```

### 4. **状态流转**

1. **初始化**：`runInput` 提供初始状态值
2. **配置应用**：`GraphConfiguration` 提供运行时配置
3. **状态更新**：在 graph 执行过程中，各个节点会更新 `ManagerGraphStateObj` 中的状态
4. **子图调用**：当需要调用 planner 或 programmer 子图时，会创建新的 session 并传递相关状态

这种设计使得 graph 的状态管理和配置管理分离，状态用于存储业务数据，配置用于控制 graph 的行为参数。
