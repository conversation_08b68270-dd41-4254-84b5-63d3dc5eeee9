### Open SWE Planner Agent 逻辑流转与状态变更全解析

本文面向工程实践，系统梳理 `Planner` 子图的控制流、节点职责、状态变量、关键路由条件与异常路径；并给出可视化 Mermaid 图与典型场景示例，帮助快速理解与调试。

---

## 1. 总览：Planner 的目标与产出

- **目标**: 在只读上下文收集阶段，基于对仓库的检索、阅读与工具调用，生成一份可执行、最小步骤的实现计划（`proposedPlan`），并对用户进行中断确认。如被接受/编辑则转交给 Programmer 执行；如用户回复需要调整，则判断是否需再次收集上下文并循环。
- **主要产出**:
  - `proposedPlanTitle`、`proposedPlan`（计划标题与条目列表）
  - `contextGatheringNotes`（上下文收集摘要）
  - 用户接受/编辑后，构造 `taskPlan` 并启动 Programmer。

---

## 2. Planner 的状态对象 PlannerGraphState

核心字段与更新来源（非完整业务注释，聚焦 Planner 相关）：

- `messages`: LangGraph 消息数组。贯穿所有节点，承载对话、工具调用结果、系统事件（如自定义节点事件）。
- `sandboxSessionId`: 沙箱会话标识。由 `initialize-sandbox` 设定，`take-actions` 与后续节点复用。
- `targetRepository`、`branchName`: 目标仓库与分支。初始化于输入与 `initialize-sandbox`。
- `githubIssueId`: 关联的 Issue 编号。用于从 GitHub 拉取初始上下文与写回计划/评论。
- `codebaseTree`: 代码树（最多 3 层）。由 `initialize-sandbox` 生成/更新，用于计划/诊断上下文。
- `documentCache`: 文档缓存（URL->内容）。由部分工具在 `take-actions` 聚合更新。
- `proposedPlanTitle`、`proposedPlan: string[]`: 由 `generate-plan` 通过 `session_plan` 工具生成。
- `contextGatheringNotes`: 由 `notetaker` 通过 `write_technical_notes` 工具生成。
- `taskPlan`: 接受/编辑计划后，由 `interrupt-proposed-plan` 创建，用于 Programmer 执行。
- `programmerSession`: Programmer 线程与 run 信息。由 `interrupt-proposed-plan` 启动 Programmer 后更新。
- `customRules`: 自定义规则。由 `initialize-sandbox` 解析注入（本地/沙箱两种模式）。
- `autoAcceptPlan`: 若为 true，`interrupt-proposed-plan` 直接进入 Programmer。
- `tokenData`: 各节点模型调用的 token 计费与缓存跟踪信息。

> 说明：`dependenciesInstalled` 在 `initialize-sandbox` 与 `take-actions` 的内部返回中出现，但不在 `PlannerGraphState` 的 zod schema 中持久化；程序在跨图（转 Programmer）时会重新通过沙箱能力同步依赖状态。

---

## 3. sandboxSessionId 生成逻辑详解

### 3.1 云端模式（Daytona 沙箱）

在云端模式下，`sandboxSessionId` 的生成遵循以下完整流程：

#### 3.1.1 沙箱创建参数
```typescript
// 默认沙箱创建参数
const DEFAULT_SANDBOX_CREATE_PARAMS: CreateSandboxFromSnapshotParams = {
  user: "daytona",                    // 固定用户标识
  snapshot: "open-swe-vcpu2-mem4-disk5", // 预配置的快照环境
  autoDeleteInterval: 15,             // 15分钟后自动删除
};
```

#### 3.1.2 沙箱创建流程
```typescript
// 1. 调用 Daytona API 创建沙箱
const sandbox = await daytonaClient().create(DEFAULT_SANDBOX_CREATE_PARAMS);

// 2. 沙箱创建成功后，Daytona 返回包含唯一 ID 的 Sandbox 对象
// sandbox.id 即为生成的 sandboxSessionId
```

#### 3.1.3 沙箱 ID 特性
- **唯一性**: 由 Daytona 服务保证全局唯一
- **格式**: 通常为 UUID 格式的字符串
- **生命周期**: 与沙箱实例绑定，沙箱删除后 ID 失效
- **持久性**: 在 Planner 整个生命周期内保持不变

### 3.2 本地模式（Local Mode）

在本地模式下，`sandboxSessionId` 采用模拟生成策略：

#### 3.2.1 模拟 ID 生成
```typescript
// 本地模式下的模拟沙箱 ID 生成
const mockSandboxId = `local-${Date.now()}-${crypto.randomBytes(16).toString("hex")}`;
```

#### 3.2.2 生成逻辑分解
- **前缀**: `local-` 标识本地模式
- **时间戳**: `Date.now()` 确保时间唯一性
- **随机字节**: `crypto.randomBytes(16).toString("hex")` 生成 32 位十六进制随机字符串
- **示例**: `local-1703123456789-a1b2c3d4e5f678901234567890123456`

#### 3.2.3 本地模式特性
- **无真实沙箱**: 跳过 Daytona 沙箱创建
- **本地文件系统**: 直接使用本地工作目录
- **ID 一致性**: 为保持代码逻辑一致性而生成模拟 ID

### 3.3 沙箱 ID 的复用与传递

#### 3.3.1 在 Planner 中的传递
```typescript
// initialize-sandbox 节点返回
return {
  sandboxSessionId: sandbox.id, // 或 mockSandboxId
  // ... 其他状态
};

// 后续节点通过 state.sandboxSessionId 访问
```

#### 3.3.2 跨图传递（Planner → Programmer）
```typescript
// interrupt-proposed-plan 中启动 Programmer 时
const programmerSession = await config.langgraph.createThread({
  configurable: {
    // ... 其他配置
    sandboxSessionId: state.sandboxSessionId, // 传递沙箱 ID
  },
});
```

#### 3.3.3 工具调用中的使用
```typescript
// 工具通过 getSandboxSessionOrThrow 获取沙箱实例
export async function getSandboxSessionOrThrow(
  input: Record<string, unknown>,
): Promise<Sandbox> {
  let sandboxSessionId = "";
  
  // 优先从工具输入参数获取
  if ("xSandboxSessionId" in input && input.xSandboxSessionId) {
    sandboxSessionId = input.xSandboxSessionId as string;
  } else {
    // 从当前图状态获取
    const state = getCurrentTaskInput<GraphState>();
    sandboxSessionId = state.sandboxSessionId;
  }

  if (!sandboxSessionId) {
    throw new Error("FAILED TO RUN COMMAND: No sandbox session ID provided");
  }

  // 本地模式返回模拟沙箱
  if (isLocalMode(config)) {
    return {
      id: sandboxSessionId || "local-mock-sandbox",
      state: "started",
    } as Sandbox;
  }

  // 云端模式获取真实沙箱
  const sandbox = await daytonaClient().get(sandboxSessionId);
  return sandbox;
}
```

### 3.4 沙箱状态管理

#### 3.4.1 沙箱停止与重启
```typescript
// generate-plan 节点中停止沙箱
if (state.sandboxSessionId && !isLocalMode(config)) {
  newSessionId = await stopSandbox(state.sandboxSessionId);
}

// stopSandbox 实现
export async function stopSandbox(sandboxSessionId: string): Promise<string> {
  const sandbox = await daytonaClient().get(sandboxSessionId);
  
  if (sandbox.state === SandboxState.STOPPED || 
      sandbox.state === SandboxState.ARCHIVED) {
    return sandboxSessionId; // 已停止，直接返回原 ID
  } else if (sandbox.state === "started") {
    await daytonaClient().stop(sandbox); // 停止沙箱
  }
  
  return sandbox.id; // 返回沙箱 ID（保持不变）
}
```

#### 3.4.2 错误恢复机制
```typescript
// getSandboxWithErrorHandling 中的错误处理
try {
  const sandbox = await daytonaClient().get(sandboxSessionId);
  
  if (sandbox.state === "started") {
    return { sandbox, codebaseTree: null, dependenciesInstalled: null };
  }
  
  if (sandbox.state === "stopped" || sandbox.state === "archived") {
    await sandbox.start(); // 重启已停止的沙箱
    return { sandbox, codebaseTree: null, dependenciesInstalled: null };
  }
  
  // 其他状态视为不可恢复，抛出异常
  throw new Error(`Sandbox in unrecoverable state: ${state}`);
} catch (error) {
  // 沙箱获取失败，需要重新创建
  logger.info("Recreating sandbox due to error");
  // ... 重新创建逻辑
}
```

### 3.5 沙箱 ID 的生命周期

```mermaid
graph TD
    A[Planner 启动] --> B{本地模式?}
    B -->|是| C[生成模拟 ID<br/>local-timestamp-random]
    B -->|否| D[调用 Daytona API<br/>创建真实沙箱]
    D --> E[获取沙箱 ID<br/>sandbox.id]
    C --> F[initialize-sandbox<br/>返回 sandboxSessionId]
    E --> F
    F --> G[Planner 执行<br/>使用 sandboxSessionId]
    G --> H[generate-plan<br/>停止沙箱]
    H --> I[interrupt-proposed-plan<br/>传递 ID 给 Programmer]
    I --> J[Programmer 启动<br/>复用 sandboxSessionId]
    J --> K[沙箱自动删除<br/>15分钟后]
```

---

## 4. 控制流（高层）

```mermaid
graph TD
  START((START)) --> A[prepare-graph-state]
  A -->|always| B[initialize-sandbox]
  B --> C[generate-plan-context-action]
  C -->|LLM给出tool_calls| D[take-plan-actions]
  C -->|无tool_calls| E[generate-plan]
  D -->|需继续收集/未超限| C
  D -->|错误诊断| F[diagnose-error]
  F --> C
  D -->|超出动作上限| E
  E --> G[notetaker]
  G --> H[interrupt-proposed-plan]
  H -->|accept/edit或auto-accept| END
  H -->|ignore| END
  H -->|response评论/回复| I[determine-needs-context]
  I -->|need_context| C
  I -->|have_context| E
```

为了便于对照，下面给出图结构在源码中的定义摘录：

```33:60:apps/open-swe/src/graphs/planner/index.ts
const workflow = new StateGraph(PlannerGraphStateObj, GraphConfiguration)
  .addNode("prepare-graph-state", prepareGraphState, { ends: [END, "initialize-sandbox"] })
  .addNode("initialize-sandbox", initializeSandbox)
  .addNode("generate-plan-context-action", generateAction)
  .addNode("take-plan-actions", takeActions, { ends: ["generate-plan-context-action", "diagnose-error", "generate-plan"] })
  .addNode("generate-plan", generatePlan)
  .addNode("notetaker", notetaker)
  .addNode("interrupt-proposed-plan", interruptProposedPlan, { ends: [END, "determine-needs-context"] })
  .addNode("determine-needs-context", determineNeedsContext, { ends: ["generate-plan-context-action", "generate-plan"] })
  .addNode("diagnose-error", diagnoseError)
  .addEdge(START, "prepare-graph-state")
  .addEdge("initialize-sandbox", "generate-plan-context-action")
  .addConditionalEdges(
    "generate-plan-context-action",
    takeActionOrGeneratePlan,
    ["take-plan-actions", "generate-plan"],
  )
  .addEdge("diagnose-error", "generate-plan-context-action")
  .addEdge("generate-plan", "notetaker")
  .addEdge("notetaker", "interrupt-proposed-plan");
```

---

## 5. 关键节点详解与状态变更

### 5.1 prepare-graph-state
- **职责**: 标准化 `messages`，补齐 GitHub Issue 与评论上下文；将上一次的 `contextGatheringNotes` 作为 AI 摘要消息注入；移除隐藏且非摘要的 AI 消息。
- **状态更新**:
  - 若无本地模式：拉取 Issue 与 comments；空对话则全部注为 `HumanMessage`；否则追加未跟踪评论 `untrackedComments`，并生成摘要 `summaryMessage`（可选）。
  - 清空 `contextGatheringNotes`（已注入到 `messages`）。
- **转移**: 永远 `goto: initialize-sandbox`。

### 5.2 initialize-sandbox（含本地模式分支）
- **职责**: 恢复/创建沙箱，拉取代码、生成代码树 `codebaseTree`，解析 `customRules`，并通过隐藏消息注入节点事件流（UI 可消费）。本地模式跳过沙箱创建与克隆，用本地工作目录生成代码树。
- **状态更新**: `sandboxSessionId`、`codebaseTree`、`messages(事件)`、`customRules`、`branchName`。
- **转移**: `goto: generate-plan-context-action`。

### 5.3 generate-plan-context-action（计划前的上下文动作决策）
- **职责**: 以 Planner LLM 作为“路由/动作生成器”，绑定多种只读工具：`grep`、`shell`、`view`、`scratchpad`、`get_url_content`、`search_document_for`、以及 MCP 工具。生成一条 AI 消息，可能包含一个或多个 `tool_calls`。
- **状态更新**: 将 `missingMessages`（GitHub 新评论等）与 LLM 响应一并加入 `messages`；同步最新 `taskPlan`（如 Issue 中已有最新计划）。
- **转移**: 由 `takeActionOrGeneratePlan` 决定：
  - 若 AI 响应包含 `tool_calls` → `take-plan-actions`
  - 否则 → `generate-plan`

### 5.4 take-plan-actions（执行只读工具调用）
- **职责**: 在沙箱（或本地）执行工具调用并聚合结果，严格只读：若检测到代码变更则自动回滚并警告；将工具输出加工（`processToolCallContent`）为 `ToolMessage` 写回对话。
- **状态更新**:
  - `messages`: 追加每个工具的 `ToolMessage`（含 status）。
  - `sandboxSessionId`、`codebaseTree`（保持最新）、`documentCache`（工具合并）、`dependenciesInstalled`（内部跟踪）。
- **转移规则**:
  - 若只读收集动作累计超限（`maxContextActions*2`，默认 `75*2`）→ `generate-plan`
  - 否则若最近动作存在错误且需要诊断 → `diagnose-error`
  - 否则 → 回到 `generate-plan-context-action` 继续只读收集循环

### 5.5 diagnose-error（错误诊断）
- **职责**: 使用 Summarizer LLM 调用 `diagnose_error` 工具，基于最近失败的工具输出与代码树生成诊断说明，帮助后续动作继续推进。
- **状态更新**: `messages`（诊断工具调用及结果）、`tokenData`。
- **转移**: 回到 `generate-plan-context-action`。

### 5.6 generate-plan（生成最终计划）
- **职责**: 使用 Planner LLM 调用 `session_plan` 工具产出 `proposedPlanTitle` 与 `proposedPlan`；如存在沙箱会话且非本地模式，则先停止沙箱以便后续中断到用户。
- **状态更新**: `messages`（包含工具调用及确认 `ToolMessage`）、`proposedPlanTitle`、`proposedPlan`、`tokenData`。
- **转移**: `goto: notetaker`。

### 5.7 notetaker（上下文笔记沉淀）
- **职责**: 使用 Summarizer LLM 调用 `write_technical_notes` 工具，将收集过程中的关键技术要点提炼为 `contextGatheringNotes`（删除原对话后 Programmer 可直接复用）。
- **状态更新**: `messages`、`contextGatheringNotes`、`tokenData`。
- **转移**: `goto: interrupt-proposed-plan`。

### 5.8 interrupt-proposed-plan（中断至用户审批/编辑）
- **职责**: 将 `proposedPlan` 中断给前端/用户，支持 `accept` / `edit` / `response` / `ignore`；可配置 `autoAcceptPlan` 跳过人工确认。
- **状态更新**:
  - `accept/edit`：构造 `taskPlan`，创建 Programmer 线程与 run（`programmerSession`），并更新 `sandboxSessionId` 等运行态；写回 GitHub 评论（非本地）。
  - `response`：不更新计划，转判断是否需要再次收集上下文。
  - `ignore`：结束。
- **转移**:
  - `accept/edit`/`auto-accept` → 启动 Programmer → `END`
  - `ignore` → `END`
  - `response` → `determine-needs-context`

### 5.9 determine-needs-context（是否需要再次收集）
- **职责**: 使用 Router LLM 调用 `determine_context` 工具，对“用户跟进信息 + 既有上下文（含 `contextGatheringNotes` 与 `proposedPlan`）”进行判定。
- **状态更新**: `messages`（补齐缺失消息）、`tokenData`。
- **转移**:
  - `need_context` → 回到 `generate-plan-context-action` 继续只读收集循环
  - `have_context` → 直接进入 `generate-plan` 修订计划

---

## 6. 关键路由与守卫条件

- **是否继续只读收集，或进入生成计划**：
  - 条件：最后一条 `AIMessage` 是否带有 `tool_calls`。
  - 源码：`takeActionOrGeneratePlan`。

- **只读循环是否超限**：
  - 条件：过滤隐藏消息后，仅统计 `AIMessage` 与 `ToolMessage` 的数量，达到 `maxContextActions*2`（默认 150）即强制进入 `generate-plan`。

- **错误诊断路由**：
  - 条件：`shouldDiagnoseError(messages)` 返回真，则 `take-plan-actions` → `diagnose-error`。

- **用户回复后的上下文需求判定**：
  - 条件：`determine_context.decision ∈ {have_context, need_context}`。

---

## 7. 典型场景与状态变化轨迹

### 场景 A：首次运行、生成计划并被接受
1) `prepare-graph-state` 拉取 Issue 与评论，构造 `messages`；→ 2) `initialize-sandbox` 生成 `sandboxSessionId`、`codebaseTree`、`customRules`；→ 3) `generate-plan-context-action` 与 `take-plan-actions` 进行若干只读检索；→ 4) `generate-plan` 产出 `proposedPlanTitle/ProposedPlan`；→ 5) `notetaker` 生成 `contextGatheringNotes`；→ 6) `interrupt-proposed-plan` 用户 `accept`；→ 7) 启动 Programmer，`END`。

关键状态快照：
- `messages`: 不断追加工具输出、计划与笔记的 `ToolMessage`/`AIMessage`。
- `proposedPlanTitle/proposedPlan`: 在 `generate-plan` 时首次出现。
- `contextGatheringNotes`: 在 `notetaker` 时出现。
- `taskPlan`、`programmerSession`: 在 `interrupt-proposed-plan` 的 `accept` 分支写入。

### 场景 B：用户编辑计划
- 与场景 A 基本一致，但在 `interrupt-proposed-plan` 中走 `edit` 分支，替换 `planItems`，生成新的 `taskPlan` 并启动 Programmer。

### 场景 C：用户回复（response）导致需要更多上下文
- `interrupt-proposed-plan` → `determine-needs-context` → 工具 `determine_context` 得到 `need_context` → 回到 `generate-plan-context-action`/`take-plan-actions` 继续只读循环，直至再次 `generate-plan` 更新计划。

### 场景 D：工具报错的诊断闭环
- 在 `take-plan-actions` 中检测到失败动作且需诊断 → `diagnose-error` 生成诊断说明 → 回到 `generate-plan-context-action` 继续只读循环。

### 场景 E：只读动作超限
- 在 `take-plan-actions` 中，若 `AIMessage/ToolMessage` 数达到阈值 → 强制进入 `generate-plan`，避免无休止的上下文收集。

### 场景 F：本地模式（local mode）
- `prepare-graph-state` 直接跳过 GitHub 拉取；`initialize-sandbox` 走 `initializeSandboxLocal`：不创建沙箱、不克隆仓库，直接用本地工作目录与本地 `codebaseTree`；计划生成与中断逻辑与云端一致（跳过 GitHub 评论写入）。

### 场景 G：自动接受计划（autoAcceptPlan）
- `interrupt-proposed-plan` 在 `autoAcceptPlan=true` 时直接创建 `taskPlan` 并启动 Programmer，顺带在 GitHub 留痕（非本地模式），跳过人工确认。

---

## 8. GitHub 与外部交互点

- 从 Issue 拉取初始上下文与评论：`prepare-graph-state`。
- 计划写回与评论：`interrupt-proposed-plan` 中的 `addProposedPlanToIssue`、`postGitHubIssueComment`、`addTaskPlanToIssue`。
- 安装 token 刷新：在 Programmer run 之前进行 `regenerateInstallationToken`。

---

## 9. 工具与模型分工

- 模型任务划分：
  - `LLMTask.PLANNER`: `generate-plan-context-action`（生成动作/可能带 tool_calls）、`generate-plan`（产出 `session_plan`）。
  - `LLMTask.SUMMARIZER`: `notetaker`（`write_technical_notes`）、`diagnose-error`（`diagnose_error`）。
  - `LLMTask.ROUTER`: `determine-needs-context`（`determine_context`）。

- 只读工具（规划阶段可用）：
  - `grep`、`view`、`shell`（只读约束，若变更将被回滚并警告）、`scratchpad`、`get_url_content`、`search_document_for`、以及 MCP 工具。

---

## 10. 调试与排障要点

- 若 Planner 循环不止：检查 `maxContextActions`（默认 75）。超限后将自动生成计划。
- 若计划缺乏可执行细节：查看 `generate-plan` 的系统提示是否正确将 `codebaseTree`、`customRules` 与 `SCRATCHPAD` 注入；必要时提升上下文收集质量。
- 若工具频繁报错：留意 `diagnose-error` 的诊断输出与 `codebaseTree` 一致性；同时关注本地/沙箱路径差异（`LOCAL_MODE_NOTE`）。
- 若 GitHub 上下文不同步：确认 `prepare-graph-state` 是否补齐了 `missingMessages`、以及是否正确过滤/注入 `summaryMessage`。

---

## 11. 参考：决定"继续动作/直接出计划"的条件

```20:31:apps/open-swe/src/graphs/planner/index.ts
function takeActionOrGeneratePlan(
  state: PlannerGraphState,
): "take-plan-actions" | "generate-plan" {
  const { messages } = state;
  const lastMessage = messages[messages.length - 1];
  if (isAIMessage(lastMessage) && lastMessage.tool_calls?.length) {
    return "take-plan-actions";
  }
  return "generate-plan";
}
```

---

以上为 Planner 的全流程与状态说明。建议配合运行日志中的自定义节点事件与 `messages` 序列来定位问题与还原现场。


