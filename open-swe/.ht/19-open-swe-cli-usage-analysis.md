# Open SWE CLI 使用方式分析

## 1. 整体架构概览

Open SWE 通过 CLI 方式使用时，采用了**本地模式 (Local Mode)** 的架构设计，不需要单独启动服务，而是直接连接到本地的 LangGraph 服务器。

```mermaid
graph TB
    A[用户运行 open-swe CLI] --> B[CLI 启动本地模式]
    B --> C[连接到本地 LangGraph 服务器]
    C --> D[触发 Agent 逻辑]
    D --> E[Manager Graph]
    E --> F[Planner Graph]
    F --> G[Programmer Graph]
    
    B --> H[设置本地模式标识]
    H --> I[x-local-mode: true]
    
    C --> J[创建 LangGraph Client]
    J --> K[连接到 localhost:2024]
    
    D --> L[创建 Thread]
    D --> M[创建 Run]
    
    E --> N[管理整体流程]
    F --> O[生成执行计划]
    G --> P[执行代码变更]
    
    Q[本地 LangGraph 服务器] --> R[端口 2024]
    R --> S[Agent Graphs]
    S --> T[认证系统]
    S --> U[API 路由]
```

## 2. 服务启动机制

### 2.1 不需要单独启动服务

**关键发现**: Open SWE CLI **不需要单独启动服务**，它直接连接到已经运行的 LangGraph 服务器。

### 2.2 服务依赖关系

```mermaid
graph LR
    A[LangGraph 服务器] --> B[端口 2024]
    B --> C[Agent 服务]
    C --> D[Manager Graph]
    C --> E[Planner Graph]
    C --> F[Programmer Graph]
    
    G[Open SWE CLI] --> H[本地模式]
    H --> I[连接到 localhost:2024]
    I --> C
    
    J[Web 应用] --> K[端口 3000]
    K --> I
```

### 2.3 服务启动顺序

1. **LangGraph 服务器** (必须首先启动)
   ```bash
   # 在 apps/open-swe 目录下
   yarn dev
   # 或使用 LangGraph CLI
   langgraphjs dev --config ../../langgraph.json
   ```

2. **Open SWE CLI** (可选，用于命令行交互)
   ```bash
   # 在 apps/cli 目录下
   yarn dev
   ```

3. **Web 应用** (可选，用于 Web 界面)
   ```bash
   # 在 apps/web 目录下
   yarn dev
   ```

## 3. CLI 本地模式核心逻辑

### 3.1 本地模式标识

```typescript
// apps/cli/src/index.tsx
// Always run in local mode
process.env.OPEN_SWE_LOCAL_MODE = "true";

console.log("🏠 Starting Open SWE CLI in Local Mode");
console.log("   Working directory:", process.cwd());
console.log("   No GitHub authentication required");
```

### 3.2 连接配置

```typescript
// apps/cli/src/streaming.ts
const LANGGRAPH_URL = process.env.LANGGRAPH_URL || "http://localhost:2024";

const newClient = new Client({
  apiUrl: LANGGRAPH_URL,
  defaultHeaders: headers, // 包含 x-local-mode: true
});
```

### 3.3 本地模式头部标识

```typescript
// packages/shared/src/constants.ts
export const LOCAL_MODE_HEADER = "x-local-mode";

// apps/cli/src/streaming.ts
const headers = {
  [LOCAL_MODE_HEADER]: "true",
};
```

## 4. Agent 触发和运行机制

### 4.1 Agent 触发流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant CLI as Open SWE CLI
    participant LangGraph as LangGraph 服务器
    participant Manager as Manager Graph
    participant Planner as Planner Graph
    participant Programmer as Programmer Graph

    User->>CLI: 输入提示词
    CLI->>LangGraph: 创建 Thread
    CLI->>LangGraph: 创建 Run (Manager Graph)
    LangGraph->>Manager: 启动 Manager Graph
    Manager->>Planner: 创建 Planner Session
    Planner->>Planner: 生成执行计划
    Planner->>CLI: 等待用户反馈 (如果需要)
    User->>CLI: 批准/拒绝计划
    CLI->>Planner: 提交反馈
    Planner->>Programmer: 创建 Programmer Session
    Programmer->>Programmer: 执行代码变更
    Programmer->>CLI: 返回执行结果
```

### 4.2 核心触发代码

```typescript
// apps/cli/src/streaming.ts
async startNewSession(prompt: string) {
  // 1. 准备运行输入
  const runInput = {
    messages: [
      {
        id: uuidv4(),
        type: "human",
        content: [{ type: "text", text: prompt }],
      },
    ],
    targetRepository: {
      owner: "local",
      repo: "local",
      branch: "main",
    },
    autoAcceptPlan: false,
  };

  // 2. 设置本地模式头部
  const headers = {
    [LOCAL_MODE_HEADER]: "true",
  };

  // 3. 创建 LangGraph 客户端
  const newClient = new Client({
    apiUrl: LANGGRAPH_URL,
    defaultHeaders: headers,
  });

  // 4. 创建新的 Thread
  const thread = await newClient.threads.create();
  const threadId = thread.thread_id;

  // 5. 创建 Run (触发 Manager Graph)
  const run = await newClient.runs.create(threadId, MANAGER_GRAPH_ID, {
    input: runInput,
    config: {
      recursion_limit: 400,
    },
    ifNotExists: "create",
    streamResumable: true,
    streamMode: OPEN_SWE_STREAM_MODE as StreamMode[],
  });

  // 6. 开始流式处理
  await this.startManagerStream(newClient, threadId, run.run_id);
}
```

## 5. Agent 运行机制详解

### 5.1 Graph 层次结构

```mermaid
graph TB
    A[Manager Graph] --> B[管理整体流程]
    B --> C[创建新会话]
    B --> D[启动 Planner]
    B --> E[协调各个组件]
    
    F[Planner Graph] --> G[分析需求]
    G --> H[生成执行计划]
    H --> I[等待用户反馈]
    I --> J[启动 Programmer]
    
    K[Programmer Graph] --> L[执行代码变更]
    L --> M[文件操作]
    L --> N[命令执行]
    L --> O[错误诊断]
    
    B --> F
    F --> K
```

### 5.2 Manager Graph 启动逻辑

```typescript
// apps/open-swe/src/graphs/manager/nodes/start-planner.ts
export async function startPlanner(
  state: ManagerGraphState,
  config: GraphConfig,
): Promise<ManagerGraphUpdate> {
  const langGraphClient = createLangGraphClient({
    defaultHeaders,
  });

  const runInput: PlannerGraphUpdate = {
    githubIssueId: state.githubIssueId,
    targetRepository: state.targetRepository,
    taskPlan: state.taskPlan,
    branchName: state.branchName ?? getBranchName(config),
    autoAcceptPlan: state.autoAcceptPlan,
    ...(followupMessage || localMode ? { messages: [followupMessage] } : {}),
  };

  const run = await langGraphClient.runs.create(
    plannerThreadId,
    PLANNER_GRAPH_ID,
    {
      input: runInput,
      config: {
        recursion_limit: 400,
        configurable: {
          ...getCustomConfigurableFields(config),
          ...(isLocalMode(config) && {
            [LOCAL_MODE_HEADER]: "true",
          }),
        },
      },
      ifNotExists: "create",
      streamResumable: true,
      streamMode: OPEN_SWE_STREAM_MODE as StreamMode[],
    },
  );

  return {
    plannerSession: {
      threadId: plannerThreadId,
      runId: run.run_id,
    },
  };
}
```

### 5.3 Planner Graph 运行逻辑

```typescript
// apps/open-swe/src/graphs/planner/nodes/proposed-plan.ts
async function startProgrammerRun(input: {
  runInput: Exclude<GraphUpdate, "taskPlan"> & { taskPlan: TaskPlan };
  state: PlannerGraphState;
  config: GraphConfig;
  newMessages?: BaseMessage[];
}) {
  const run = await langGraphClient.runs.create(
    programmerThreadId,
    PROGRAMMER_GRAPH_ID,
    {
      input: runInput,
      config: {
        recursion_limit: 400,
        configurable: {
          ...getCustomConfigurableFields(config),
          ...(isLocalMode(config) && { [LOCAL_MODE_HEADER]: "true" }),
        },
      },
      ifNotExists: "create",
      streamResumable: true,
      streamSubgraphs: true,
      streamMode: OPEN_SWE_STREAM_MODE as StreamMode[],
    },
  );

  return new Command({
    goto: END,
    update: {
      programmerSession: {
        threadId: programmerThreadId,
        runId: run.run_id,
      },
      sandboxSessionId: runInput.sandboxSessionId,
      taskPlan: runInput.taskPlan,
      messages: newMessages,
    },
  });
}
```

## 6. 本地模式认证机制

### 6.1 认证流程

```typescript
// apps/open-swe/src/security/auth.ts
export const auth = new Auth()
  .authenticate<AuthenticateReturn>(async (request: Request) => {
    // 1. 检查本地模式
    const localModeHeader = request.headers.get(LOCAL_MODE_HEADER);
    const isRunningLocalModeEnv = process.env.OPEN_SWE_LOCAL_MODE === "true";
    
    if (localModeHeader === "true" && isRunningLocalModeEnv) {
      return {
        identity: "local-user",
        is_authenticated: true,
        display_name: "Local User",
        metadata: {
          installation_name: "local-mode",
        },
        permissions: LANGGRAPH_USER_PERMISSIONS,
      };
    }
    
    // 2. 其他认证方式...
  });
```

### 6.2 本地模式特点

- **无需 GitHub 认证**: 本地模式不需要 GitHub 账号或令牌
- **简化权限**: 本地用户拥有完整权限
- **本地存储**: 所有数据存储在本地
- **开发友好**: 适合本地开发和测试

## 7. 流式处理机制

### 7.1 流式数据流

```mermaid
graph LR
    A[Manager Stream] --> B[Planner Stream]
    B --> C[Programmer Stream]
    C --> D[CLI 显示]
    
    E[用户输入] --> F[StreamingService]
    F --> G[LangGraph Client]
    G --> H[Thread/Run]
    H --> I[Graph Execution]
    I --> J[Stream Updates]
    J --> K[CLI UI 更新]
```

### 7.2 流式处理代码

```typescript
// apps/cli/src/streaming.ts
private async startManagerStream(
  client: Client,
  threadId: string,
  runId: string,
) {
  let plannerStreamed = false;

  for await (const chunk of client.runs.joinStream(threadId, runId)) {
    if (chunk.event === "updates") {
      const formatted = formatDisplayLog(chunk);
      if (formatted.length > 0) {
        this.callbacks.setLogs((prev) => {
          if (prev.length === 0) {
            this.callbacks.setLoadingLogs(false);
          }
          return [...prev, ...formatted];
        });
      }
    }

    // 检查 Planner Session
    if (
      !plannerStreamed &&
      chunk.data?.plannerSession?.threadId &&
      typeof chunk.data.plannerSession.threadId === "string" &&
      typeof chunk.data.plannerSession.runId === "string"
    ) {
      plannerStreamed = true;
      this.callbacks.setPlannerThreadId(chunk.data.plannerSession.threadId);

      const result = await this.handlePlannerStream(
        client,
        chunk.data.plannerSession.threadId,
        chunk.data.plannerSession.runId,
      );

      if (result.needsFeedback) {
        this.callbacks.setStreamingPhase("awaitingFeedback");
        return; // 暂停流式处理，等待用户反馈
      }
    }
  }

  this.callbacks.setStreamingPhase("done");
}
```

## 8. 本地服务架构

### 8.1 服务组件

```mermaid
graph TB
    A[本地 LangGraph 服务器] --> B[端口 2024]
    B --> C[Agent Graphs]
    C --> D[Manager Graph]
    C --> E[Planner Graph]
    C --> F[Programmer Graph]
    
    B --> G[认证系统]
    G --> H[本地模式认证]
    G --> I[GitHub 认证]
    
    B --> J[API 路由]
    J --> K[Threads API]
    J --> L[Runs API]
    J --> M[Streams API]
    
    N[Open SWE CLI] --> O[本地模式]
    O --> P[连接到 localhost:2024]
    P --> B
    
    Q[Web 应用] --> R[Web 界面]
    R --> P
```

### 8.2 服务启动方式

#### 开发模式启动
```bash
# 在 apps/open-swe 目录下
yarn dev
# 这会启动 LangGraph 开发服务器在端口 2024
```

#### 生产模式启动
```bash
# 使用 LangGraph CLI
langgraphjs up --port 2024 --config ../../langgraph.json
```

## 9. 总结

### 9.1 关键要点

1. **不需要单独启动服务**: Open SWE CLI 直接连接到已运行的 LangGraph 服务器
2. **本地模式**: 使用 `x-local-mode: true` 头部标识本地模式
3. **简化认证**: 本地模式无需 GitHub 认证
4. **流式处理**: 实时显示 Agent 执行过程
5. **交互式反馈**: 支持用户对计划进行批准/拒绝

### 9.2 使用流程

1. **启动 LangGraph 服务器** (必须)
   ```bash
   cd apps/open-swe
   yarn dev
   ```

2. **启动 Open SWE CLI** (可选)
   ```bash
   cd apps/cli
   yarn dev
   ```

3. **输入提示词**: CLI 会连接到本地服务器并触发 Agent 逻辑

4. **查看执行过程**: 实时显示 Manager → Planner → Programmer 的执行过程

5. **提供反馈**: 在需要时对计划进行批准或拒绝

### 9.3 架构优势

- **开发友好**: 本地模式简化了开发和测试流程
- **实时反馈**: 流式处理提供实时的执行状态
- **模块化设计**: 各个 Graph 职责清晰，便于维护
- **灵活部署**: 支持本地开发和生产部署两种模式

这种设计使得 Open SWE 既可以在本地进行开发和测试，也可以部署到生产环境中使用。
