# Open-SWE 中 planTask 与 proposedPlan 的关系分析

## 1. 概述

在 Open-SWE 的处理流程中，`planTask` 和 `proposedPlan` 是两个核心概念，它们代表了计划生成和执行的不同阶段。理解它们的关系对于掌握整个系统的工作流程至关重要。

## 2. 基本定义

### 2.1 proposedPlan（候选计划）
- **类型**: `string[]` - 字符串数组
- **生成时机**: Planner 阶段的 `generate-plan` 节点
- **用途**: 作为候选方案展示给用户，等待用户审核
- **特点**: 
  - 简单的字符串列表，每个元素代表一个执行步骤
  - 由 LLM 通过 `session_plan` 工具生成
  - 存储在 `PlannerGraphState.proposedPlan` 中

### 2.2 taskPlan（任务计划）
- **类型**: `TaskPlan` - 复杂的数据结构
- **生成时机**: 用户接受/编辑 proposedPlan 后，在 `interrupt-proposed-plan` 节点
- **用途**: 作为 Programmer 阶段的执行依据
- **特点**:
  - 包含完整的任务管理信息
  - 支持版本控制和状态跟踪
  - 存储在 `PlannerGraphState.taskPlan` 中

## 3. 数据结构对比

### 3.1 proposedPlan 结构
```typescript
// 简单的字符串数组
proposedPlan: string[] = [
  "步骤1：分析现有代码结构",
  "步骤2：实现新的功能模块", 
  "步骤3：编写测试用例",
  "步骤4：更新文档"
]
```

### 3.2 taskPlan 结构
```typescript
type TaskPlan = {
  tasks: Task[];           // 任务列表
  activeTaskIndex: number; // 当前激活的任务索引
};

type Task = {
  id: string;                    // 唯一标识符
  taskIndex: number;             // 任务索引
  request: string;               // 原始用户请求
  title: string;                 // 任务标题
  createdAt: number;             // 创建时间
  completed: boolean;            // 是否完成
  completedAt?: number;          // 完成时间
  summary?: string;              // 完成摘要
  planRevisions: PlanRevision[]; // 计划版本历史
  activeRevisionIndex: number;   // 当前激活的版本索引
  parentTaskId?: string;         // 父任务ID
  pullRequestNumber?: number;    // 关联的PR编号
};

type PlanRevision = {
  revisionIndex: number;         // 版本索引
  plans: PlanItem[];             // 计划项列表
  createdAt: number;             // 创建时间
  createdBy: "agent" | "user";   // 创建者
};

type PlanItem = {
  index: number;                 // 执行顺序
  plan: string;                  // 计划内容
  completed: boolean;            // 是否完成
  summary?: string;              // 完成摘要
};
```

## 4. 转换流程

### 4.1 proposedPlan → taskPlan 的转换过程

在 `interrupt-proposed-plan` 节点中，当用户接受或编辑计划时，会发生以下转换：

#### 4.1.1 用户接受计划（accept）
```typescript
if (humanResponse.type === "accept") {
  // 1. 将 proposedPlan 转换为 planItems
  planItems = proposedPlan.map((p, index) => ({
    index,
    plan: p,
    completed: false,
  }));

  // 2. 创建新的 taskPlan
  runInput.taskPlan = createNewTask(
    userTaskRequest,           // 用户请求
    state.proposedPlanTitle,   // 计划标题
    planItems,                 // 转换后的计划项
    { existingTaskPlan: state.taskPlan } // 现有任务计划（可选）
  );
}
```

#### 4.1.2 用户编辑计划（edit）
```typescript
else if (humanResponse.type === "edit") {
  // 1. 解析用户编辑的文本
  const editedPlan = (humanResponse.args as ActionRequest).args.plan
    .split(PLAN_INTERRUPT_DELIMITER)
    .map((step: string) => step.trim());

  // 2. 转换为 planItems
  planItems = editedPlan.map((p: string, index: number) => ({
    index,
    plan: p,
    completed: false,
  }));

  // 3. 创建新的 taskPlan
  runInput.taskPlan = createNewTask(
    userTaskRequest,
    state.proposedPlanTitle,
    planItems,
    { existingTaskPlan: state.taskPlan }
  );
}
```

### 4.2 createNewTask 函数的作用

```typescript
export function createNewTask(
  request: string,           // 用户请求
  title: string,             // 任务标题
  planItems: PlanItem[],     // 计划项
  options?: {
    existingTaskPlan?: TaskPlan;  // 现有任务计划
    parentTaskId?: string;        // 父任务ID
  },
): TaskPlan {
  // 创建初始计划版本
  const initialRevision: PlanRevision = {
    revisionIndex: 0,
    plans: planItems,
    createdAt: Date.now(),
    createdBy: "agent",
  };

  // 创建新任务
  const newTask: Task = {
    id: uuidv4(),
    taskIndex: existingTaskPlan ? existingTaskPlan.tasks.length : 0,
    request,
    title,
    createdAt: Date.now(),
    completed: false,
    planRevisions: [initialRevision],
    activeRevisionIndex: 0,
    parentTaskId: options?.parentTaskId,
  };

  // 返回更新后的任务计划
  if (existingTaskPlan) {
    return {
      tasks: [...existingTaskPlan.tasks, newTask],
      activeTaskIndex: existingTaskPlan.tasks.length,
    };
  }

  return {
    tasks: [newTask],
    activeTaskIndex: 0,
  };
}
```

## 5. 生命周期对比

### 5.1 proposedPlan 生命周期
1. **生成**: `generate-plan` 节点通过 LLM 生成
2. **展示**: 在 UI 中展示给用户审核
3. **中断**: `interrupt-proposed-plan` 等待用户操作
4. **转换**: 用户接受/编辑后转换为 taskPlan
5. **结束**: 转换完成后不再使用

### 5.2 taskPlan 生命周期
1. **创建**: 从 proposedPlan 转换而来
2. **持久化**: 写入 GitHub Issue 正文
3. **执行**: 传递给 Programmer 进行实际执行
4. **更新**: 在执行过程中不断更新状态
5. **完成**: 任务完成后标记为完成状态

## 6. 存储和持久化

### 6.1 proposedPlan 存储
- **内存**: 存储在 `PlannerGraphState.proposedPlan`
- **GitHub**: 通过 `addProposedPlanToIssue` 写入 Issue 正文
- **格式**: JSON 字符串，用特殊标签包裹

### 6.2 taskPlan 存储
- **内存**: 存储在 `PlannerGraphState.taskPlan`
- **GitHub**: 通过 `addTaskPlanToIssue` 写入 Issue 正文
- **格式**: 完整的 TaskPlan JSON 结构，用特殊标签包裹

### 6.3 恢复机制
```typescript
// 从 GitHub Issue 恢复计划
export async function getPlansFromIssue(
  input: GetIssueTaskPlanInput,
  config: GraphConfig,
): Promise<{
  taskPlan: TaskPlan | null;
  proposedPlan: string[] | null;
}> {
  // 解析 Issue 正文中的两个标签段
  const taskPlan = extractTasksFromIssueContent(issue.body);
  const proposedPlan = extractProposedPlanFromIssueContent(issue.body);
  return { taskPlan, proposedPlan };
}
```

## 7. 使用场景

### 7.1 proposedPlan 使用场景
- **计划生成**: Planner 生成候选方案
- **用户审核**: 前端展示计划供用户审核
- **用户编辑**: 用户修改计划内容
- **计划拒绝**: 用户忽略或拒绝计划

### 7.2 taskPlan 使用场景
- **任务执行**: Programmer 根据 taskPlan 执行任务
- **进度跟踪**: 跟踪每个任务的完成状态
- **版本管理**: 管理计划的不同版本
- **状态持久化**: 跨进程/服务恢复执行状态

## 8. 关键差异总结

| 特性 | proposedPlan | taskPlan |
|------|-------------|----------|
| **数据类型** | `string[]` | `TaskPlan` 复杂对象 |
| **生成阶段** | Planner 阶段 | 用户确认后 |
| **用途** | 候选方案展示 | 实际执行依据 |
| **生命周期** | 临时性 | 持久性 |
| **状态跟踪** | 无 | 完整的任务状态 |
| **版本控制** | 无 | 支持版本历史 |
| **用户交互** | 接受/编辑/拒绝 | 执行/暂停/完成 |

## 9. 代码示例

### 9.1 典型的转换流程
```typescript
// 1. Planner 生成 proposedPlan
const proposedPlan = ["步骤1", "步骤2", "步骤3"];

// 2. 用户接受后转换为 taskPlan
const planItems = proposedPlan.map((p, index) => ({
  index,
  plan: p,
  completed: false,
}));

const taskPlan = createNewTask(
  "用户请求",
  "任务标题", 
  planItems,
  { existingTaskPlan: null }
);

// 3. 传递给 Programmer 执行
await startProgrammerRun({
  runInput: { ...runInput, taskPlan },
  state,
  config,
  newMessages: [...]
});
```

## 10. 总结

`proposedPlan` 和 `taskPlan` 代表了 Open-SWE 中计划管理的两个不同阶段：

- **proposedPlan** 是 Planner 生成的候选方案，简单直接，用于用户审核
- **taskPlan** 是经过用户确认后的正式执行计划，结构复杂，支持完整的任务管理

它们之间的转换发生在用户确认阶段，通过 `createNewTask` 函数将简单的字符串数组转换为完整的任务管理结构，为后续的执行阶段提供坚实的基础。
