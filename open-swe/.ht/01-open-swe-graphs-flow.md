# Open SWE Graphs 流转图分析

## 概述

Open SWE 项目包含四个主要的 LangGraph 工作流，每个都有特定的职责和流转逻辑：

1. **Manager Graph** - 消息分类和会话管理
2. **Planner Graph** - 任务规划和计划生成
3. **Programmer Graph** - 代码执行和任务完成
4. **Reviewer Graph** - 代码审查和质量保证

## 1. Manager Graph 流转图

```mermaid
graph TD
    START([开始]) --> A[initialize-github-issue<br/>初始化GitHub Issue]
    A --> B[classify-message<br/>消息分类]
    
    B --> C{分类结果}
    C -->|END| END1([结束])
    C -->|start-planner| D[start-planner<br/>启动规划器]
    C -->|create-new-session| E[create-new-session<br/>创建新会话]
    
    D --> END2([结束])
    E --> END3([结束])
    
    style START fill:#e1f5fe
    style END1 fill:#ffebee
    style END2 fill:#ffebee
    style END3 fill:#ffebee
    style B fill:#fff3e0
```

### Manager Graph 节点说明

- **initialize-github-issue**: 初始化GitHub Issue，获取issue信息和上下文
- **classify-message**: 对用户消息进行分类，决定下一步行动
- **start-planner**: 启动Planner Graph进行任务规划
- **create-new-session**: 创建新的工作会话

## 2. Planner Graph 流转图

```mermaid
graph TD
    START([开始]) --> A[prepare-graph-state<br/>准备图状态]
    A --> B{状态检查}
    B -->|END| END1([结束])
    B -->|继续| C[initialize-sandbox<br/>初始化沙盒环境]
    
    C --> D[generate-plan-context-action<br/>生成计划上下文动作]
    D --> E{是否有工具调用?}
    E -->|是| F[take-plan-actions<br/>执行计划动作]
    E -->|否| G[generate-plan<br/>生成计划]
    
    F --> H{动作结果}
    H -->|需要上下文| I[determine-needs-context<br/>确定需要上下文]
    H -->|错误| J[diagnose-error<br/>诊断错误]
    H -->|继续| D
    
    I --> K{需要上下文?}
    K -->|是| D
    K -->|否| G
    
    J --> D
    
    G --> L[notetaker<br/>记录笔记]
    L --> M[interrupt-proposed-plan<br/>中断提议计划]
    M --> N{计划状态}
    N -->|END| END2([结束])
    N -->|需要上下文| I
    
    style START fill:#e1f5fe
    style END1 fill:#ffebee
    style END2 fill:#ffebee
    style D fill:#fff3e0
    style G fill:#fff3e0
```

### Planner Graph 节点说明

- **prepare-graph-state**: 准备图状态，检查是否需要初始化
- **initialize-sandbox**: 初始化沙盒环境
- **generate-plan-context-action**: 生成计划相关的上下文动作
- **take-plan-actions**: 执行计划相关的工具调用
- **generate-plan**: 生成任务计划
- **notetaker**: 记录工作笔记和进度
- **interrupt-proposed-plan**: 处理计划中断
- **determine-needs-context**: 确定是否需要更多上下文
- **diagnose-error**: 诊断和修复错误

## 3. Programmer Graph 流转图

```mermaid
graph TD
    START([开始]) --> A[initialize<br/>初始化沙盒]
    A --> B[generate-action<br/>生成动作]
    
    B --> C{动作类型}
    C -->|工具调用| D[take-action<br/>执行动作]
    C -->|请求帮助| E[request-help<br/>请求人类帮助]
    C -->|更新计划| F[update-plan<br/>更新计划]
    C -->|完成任务| G[handle-completed-task<br/>处理完成任务]
    C -->|路由到审查| H[route-to-review-or-conclusion<br/>路由到审查或结论]
    C -->|继续生成| B
    
    D --> I{执行结果}
    I -->|错误| J[diagnose-error<br/>诊断错误]
    I -->|成功| B
    
    E --> K{帮助结果}
    K -->|继续| B
    K -->|结束| END1([结束])
    
    F --> B
    G --> L{任务状态}
    L -->|总结历史| M[summarize-history<br/>总结历史]
    L -->|继续| B
    L -->|路由| H
    
    H --> N{审查决策}
    N -->|生成结论| O[generate-conclusion<br/>生成结论]
    N -->|审查| P[reviewer-subgraph<br/>审查子图]
    
    P --> Q{审查结果}
    Q -->|完成| O
    Q -->|继续| B
    
    O --> R{结论类型}
    R -->|开PR| S[open-pr<br/>创建Pull Request]
    R -->|结束| END2([结束])
    
    J --> B
    M --> B
    S --> END3([结束])
    
    style START fill:#e1f5fe
    style END1 fill:#ffebee
    style END2 fill:#ffebee
    style END3 fill:#ffebee
    style B fill:#fff3e0
    style P fill:#e8f5e8
```

### Programmer Graph 节点说明

- **initialize**: 初始化沙盒环境
- **generate-action**: 生成下一个要执行的动作
- **take-action**: 执行工具调用动作
- **request-help**: 请求人类帮助
- **update-plan**: 更新任务计划
- **handle-completed-task**: 处理已完成的任务
- **route-to-review-or-conclusion**: 决定是进行审查还是生成结论
- **reviewer-subgraph**: 调用Reviewer Graph进行代码审查
- **generate-conclusion**: 生成工作总结
- **open-pr**: 创建Pull Request
- **diagnose-error**: 诊断和修复错误
- **summarize-history**: 总结工作历史

## 4. Reviewer Graph 流转图

```mermaid
graph TD
    START([开始]) --> A[initialize-state<br/>初始化状态]
    A --> B[generate-review-actions<br/>生成审查动作]
    
    B --> C{是否有工具调用?}
    C -->|是| D[take-review-actions<br/>执行审查动作]
    C -->|否| E[final-review<br/>最终审查]
    
    D --> F{动作结果}
    F -->|错误| G[diagnose-reviewer-error<br/>诊断审查错误]
    F -->|继续| B
    F -->|完成| E
    
    G --> B
    E --> END([结束])
    
    style START fill:#e1f5fe
    style END fill:#ffebee
    style B fill:#fff3e0
    style E fill:#e8f5e8
```

### Reviewer Graph 节点说明

- **initialize-state**: 初始化审查状态
- **generate-review-actions**: 生成审查相关的动作
- **take-review-actions**: 执行审查工具调用
- **final-review**: 生成最终审查报告
- **diagnose-reviewer-error**: 诊断审查过程中的错误

## 5. Shared 共享组件

```mermaid
graph LR
    A[diagnose-error.ts<br/>错误诊断] --> B[所有Graphs]
    C[initialize-sandbox.ts<br/>沙盒初始化] --> D[Planner & Programmer]
    E[prompts.ts<br/>提示模板] --> F[所有Graphs]
    
    style A fill:#f3e5f5
    style C fill:#f3e5f5
    style E fill:#f3e5f5
```

### Shared 组件说明

- **diagnose-error.ts**: 统一的错误诊断和处理逻辑
- **initialize-sandbox.ts**: 沙盒环境初始化，用于代码执行
- **prompts.ts**: 共享的提示模板

## 6. 整体工作流程

```mermaid
graph TD
    A[GitHub Issue] --> B[Manager Graph]
    B --> C{消息分类}
    C -->|新任务| D[Planner Graph]
    C -->|继续任务| E[Programmer Graph]
    
    D --> F[生成计划]
    F --> E
    
    E --> G{需要审查?}
    G -->|是| H[Reviewer Graph]
    G -->|否| I[继续执行]
    
    H --> J{审查结果}
    J -->|通过| K[完成任务]
    J -->|需要修改| E
    
    I --> L{任务完成?}
    L -->|是| M[创建PR]
    L -->|否| E
    
    style A fill:#e1f5fe
    style B fill:#fff3e0
    style D fill:#fff3e0
    style E fill:#fff3e0
    style H fill:#e8f5e8
    style M fill:#c8e6c9
```

## 关键特性

1. **模块化设计**: 每个Graph都有明确的职责边界
2. **错误处理**: 统一的错误诊断和处理机制
3. **状态管理**: 完善的状态传递和持久化
4. **工具集成**: 丰富的工具调用支持
5. **审查机制**: 内置的代码审查流程
6. **沙盒环境**: 安全的代码执行环境

## 数据流转

- **Manager → Planner**: 传递任务需求和上下文
- **Planner → Programmer**: 传递任务计划和沙盒环境
- **Programmer → Reviewer**: 传递代码变更和上下文
- **Reviewer → Programmer**: 传递审查结果和修改建议

这个架构设计确保了代码生成、执行和审查的完整闭环，提供了高质量的软件开发自动化解决方案。
