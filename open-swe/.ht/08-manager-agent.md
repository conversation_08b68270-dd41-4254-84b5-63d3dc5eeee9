### Open SWE Manager 子图 逻辑流转与状态变更全解析

本篇延续 Planner/Programmer/Reviewer 文档风格，系统梳理 Manager 子图（会话编排入口）的控制流、节点职责、状态变量、关键路由与典型场景；附 Mermaid 可视化与关键源码摘录。

---

## 1. Manager 的目标与产出

- 目标: 作为系统入口与协调者，接收用户输入（或 GitHub Issue），进行分类与路由：创建新 Session、启动 Planner、或直接结束；在必要时创建/补全文档（Issue/评论），并维护 Planner 会话状态。
- 主要产出:
  - 创建并补全 GitHub Issue 与评论（非本地模式）
  - 决策与路由：`create-new-session`、`start-planner`、或 `END`
  - 维护 `plannerSession`（threadId/runId）

---

## 2. Manager 的状态（ManagerGraphState）要点

- `messages`：入口对话（含原始用户请求）
- `githubIssueId`、`githubPullRequestId?`
- `targetRepository`、`branchName`
- `taskPlan`：历史/现有计划（可从 Issue 中提取并覆盖）
- `plannerSession?`、`programmerSession?`
- `autoAcceptPlan?`：交由 Planner 行为控制

---

## 3. 控制流（高层）

```mermaid
graph TD
  START((START)) --> A[initialize-github-issue]
  A --> B[classify-message]
  B -->|no_op| END
  B -->|create_new_issue| C[create-new-session]
  B -->|start_planner / start_planner_for_followup| D[start-planner]
  C --> END
  D --> END
```

源码对应：

```16:29:apps/open-swe/src/graphs/manager/index.ts
const workflow = new StateGraph(ManagerGraphStateObj, GraphConfiguration)
  .addNode("initialize-github-issue", initializeGithubIssue)
  .addNode("classify-message", classifyMessage, { ends: [END, "start-planner", "create-new-session"] })
  .addNode("create-new-session", createNewSession)
  .addNode("start-planner", startPlanner)
  .addEdge(START, "initialize-github-issue")
  .addEdge("initialize-github-issue", "classify-message")
  .addEdge("create-new-session", END)
  .addEdge("start-planner", END)
```

---

## 4. 关键节点与状态变更

### 4.1 initialize-github-issue
- 非本地模式：
  - 若已有 `HumanMessage`：尝试从 Issue 体中抽取任务计划 `<open-swe-do-not-edit-task-plan>` 覆盖 `taskPlan`
  - 若尚无 `HumanMessage`：根据 `githubIssueId` 读取 Issue 内容注入为 `HumanMessage`；并尝试抽取任务计划
- 本地模式：直接返回（CLI 已注入 `HumanMessage`）
- 更新：`messages?`、`taskPlan?`

### 4.2 classify-message（分类并路由）
- 上下文：获取 Planner/Programmer 的线程状态；如有 `githubIssueId`，拉取最新计划以便分类时参考
- 工具：`respond_and_route`（Router 模型）
- 路由：
  - `no_op`：仅追加模型回复并 `END`
  - `create_new_issue`：→ `create-new-session`
  - `start_planner` / `start_planner_for_followup`：→ `start-planner`
  - 本地模式：仅允许 `start_planner` 或 `start_planner_for_followup`
- GitHub 同步：
  - 若无 `githubIssueId`：创建 Issue，并将原始 `HumanMessage` 回写为带 `githubIssueId` 的消息
  - 若已有 `githubIssueId` 且有多条 `HumanMessage`：为缺失 `githubIssueId` 的消息创建 Issue 评论并写回
- 若 Planner 处于中断态且需跟进：调用 `runs.create(..., {command: {resume: HumanResponse { type: 'response' }}})` 恢复
- 更新：`messages`、`githubIssueId?`、`plannerSession?`

### 4.3 create-new-session（创建新会话并链式启动）
- 从对话中提取 Issue 标题/内容，创建新 Issue
- 拼装新的 `HumanMessage`（含 Issue 标签块），并追加告知用户的新 `AIMessage`
- 通过 LangGraph SDK `runs.create` 启动新的 Manager 线程，直接 `goto: start-planner`
- 更新（当前线程）：发送“创建成功”提示消息

### 4.4 start-planner（启动 Planner）
- 生成或复用 `plannerThreadId`；准备 `PlannerGraphUpdate`：`githubIssueId`、`targetRepository`、`taskPlan`、`branchName`、`autoAcceptPlan?`、`messages?`（跟进消息）
- 非本地模式前置：如非评测令牌则刷新安装令牌；注入默认 headers
- 调用 LangGraph SDK `runs.create` 启动 Planner；返回 `plannerSession.threadId/runId`

---

## 5. 典型场景与状态变化轨迹

### 场景 A：首次进入，创建 Issue 并启动 Planner
1) `initialize-github-issue` 注入 `HumanMessage` → 2) `classify-message` → 路由 `start_planner` → 3) `start-planner` 启动 Planner 并返回 `plannerSession`

### 场景 B：跟进评论，追加到 Issue 并恢复 Planner
- `classify-message` 识别 `start_planner_for_followup`，为缺失 `githubIssueId` 的评论补写 Issue 评论记录，必要时恢复 Planner 中断，返回新的 `runId`

### 场景 C：创建新会话
- `classify-message` 识别 `create_new_issue` → `create-new-session` 创建Issue并在新 Manager 线程中 `goto: start-planner`，当前线程仅提示成功

---

## 6. 调试与排障建议

- 若分类异常：检查 `respond_and_route` 的输入上下文（Planner/Programmer 线程状态、Issue 计划文本、请求源）是否齐全
- 若 GitHub 同步异常：确认 Access Token 权限与 `extractTasksFromIssueContent` 标记是否正确
- 若 Planner 恢复失败：确认 `plannerSession.threadId` 与 `runs.create(..., command.resume)` 的参数

---

以上为 Manager 子图的全流程与状态说明。


