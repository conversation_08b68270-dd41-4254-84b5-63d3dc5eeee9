## classifyMessage 逻辑梳理与示例

### 1) 入口与目标

```56:64:apps/open-swe/src/graphs/manager/nodes/classify-message/index.ts
export async function classifyMessage(
  state: ManagerGraphState,
  config: GraphConfig,
): Promise<Command> {
  const userMessage = state.messages.findLast(isHumanMessage);
  if (!userMessage) {
    throw new Error("No human message found.");
  }
```

作用：对最新的 Human 消息进行分类并路由到合适的处理路径（直接回复、启动/更新 Planner、更新 Programmer、恢复 Planner、创建新会话等）。

### 2) 线程状态与上下文收集

```69:88:apps/open-swe/src/graphs/manager/nodes/classify-message/index.ts
  if (!isLocalMode(config)) {
    // Only create LangGraph client if not in local mode
    langGraphClient = createLangGraphClient({
      defaultHeaders: getDefaultHeaders(config),
    });

    plannerThread = state.plannerSession?.threadId
      ? await langGraphClient.threads.get(state.plannerSession.threadId)
      : undefined;
    const plannerThreadValues = plannerThread?.values;
    programmerThread = plannerThreadValues?.programmerSession?.threadId
      ? await langGraphClient.threads.get(
          plannerThreadValues.programmerSession.threadId,
        )
      : undefined;
  }

  const programmerStatus = programmerThread?.status ?? "not_started";
  const plannerStatus = plannerThread?.status ?? "not_started";
```

若 `state.githubIssueId` 存在，会从 issue 读取最新计划与候选方案，缺省回退到 `state.taskPlan`。

```89:104:apps/open-swe/src/graphs/manager/nodes/classify-message/index.ts
  const issuePlans = state.githubIssueId
    ? await getPlansFromIssue(state, config)
    : null;
  const taskPlan = issuePlans?.taskPlan ?? state.taskPlan;

  const { prompt, schema } = createClassificationPromptAndToolSchema({
    programmerStatus,
    plannerStatus,
    messages: state.messages,
    taskPlan,
    proposedPlan: issuePlans?.proposedPlan ?? undefined,
    requestSource: userMessage.additional_kwargs?.requestSource as
      | RequestSource
      | undefined,
  });
```

### 3) 动态路由选项与提示词构建

```118:139:apps/open-swe/src/graphs/manager/nodes/classify-message/utils.ts
  const programmerRunning = inputs.programmerStatus === "busy";
  const plannerRunning = inputs.plannerStatus === "busy";
  const plannerInterrupted = inputs.plannerStatus === "interrupted";
  const plannerNotStarted = inputs.plannerStatus === "not_started";
  const plannerAndProgrammerIdle =
    inputs.programmerStatus === "idle" && inputs.plannerStatus === "idle";

  const showCreateIssueOption =
    inputs.programmerStatus !== "not_started" ||
    inputs.plannerStatus !== "not_started";

  const routingOptions = [
    ...(programmerRunning ? ["update_programmer"] : []),
    ...(plannerNotStarted ? ["start_planner"] : []),
    ...(plannerAndProgrammerIdle ? ["start_planner_for_followup"] : []),
    ...(plannerRunning ? ["update_planner"] : []),
    ...(plannerInterrupted ? ["resume_and_update_planner"] : []),
    ...(showCreateIssueOption ? ["create_new_issue"] : []),
    "no_op",
  ];
```

提示词会注入：程序员/规划器状态、历史对话（移除了最后一条用户消息）、任务计划或候选方案、请求来源等，Schema 则基于上述动态选项扩展自 `BASE_CLASSIFICATION_SCHEMA`。

```3:17:apps/open-swe/src/graphs/manager/nodes/classify-message/schemas.ts
export const BASE_CLASSIFICATION_SCHEMA = z.object({
  internal_reasoning: z
    .string()
    .describe(
      "The reasoning being the decision of the route you're going to take...",
    ),
  response: z
    .string()
    .describe(
      "The response to send to the user...",
    ),
  route: z
    .enum(["no_op"]) // 动态扩展
    .describe("The route to take to handle the user's new message."),
});
```

### 4) 调用模型与工具

固定使用工具 `respond_and_route`，强制进行一次工具调用以拿到结构化 `route/response`。

```110:123:apps/open-swe/src/graphs/manager/nodes/classify-message/index.ts
  const model = await loadModel(config, LLMTask.ROUTER);
  const modelSupportsParallelToolCallsParam = supportsParallelToolCallsParam(
    config,
    LLMTask.ROUTER,
  );
  const modelWithTools = model.bindTools([respondAndRouteTool], {
    tool_choice: respondAndRouteTool.name,
    ...(modelSupportsParallelToolCallsParam
      ? { parallel_tool_calls: false }
      : {}),
  });
```

### 5) 各路由分支与行为

- no_op：仅回复消息并结束

```145:154:apps/open-swe/src/graphs/manager/nodes/classify-message/index.ts
  if (toolCallArgs.route === "no_op") {
    const commandUpdate: ManagerGraphUpdate = {
      messages: [response],
    };
    return new Command({ update: commandUpdate, goto: END });
  }
```

- create_new_issue：切到新会话节点（让管理器重新开始一次新的线程）

```156:165:apps/open-swe/src/graphs/manager/nodes/classify-message/index.ts
  if ((toolCallArgs.route as string) === "create_new_issue") {
    const commandUpdate: ManagerGraphUpdate = { messages: [response] };
    return new Command({ update: commandUpdate, goto: "create-new-session" });
  }
```

- Local 模式：仅支持启动 Planner（含 follow-up），否则报错

```167:186:apps/open-swe/src/graphs/manager/nodes/classify-message/index.ts
  if (isLocalMode(config)) {
    const newMessages: BaseMessage[] = [response];
    const commandUpdate: ManagerGraphUpdate = { messages: newMessages };
    if (
      toolCallArgs.route === "start_planner" ||
      toolCallArgs.route === "start_planner_for_followup"
    ) {
      return new Command({ update: commandUpdate, goto: "start-planner" });
    }
    throw new Error(`Unsupported route for local mode received: ${toolCallArgs.route}`);
  }
```

- 非 Local 模式：确保 GitHub Issue 存在；必要时把遗漏的人类消息追加为评论，并在返回的消息中替换这些 Human 消息，附上 `githubIssueId`、`githubIssueCommentId`、`isOriginalIssue`、`isFollowup` 等标记。

```194:231:apps/open-swe/src/graphs/manager/nodes/classify-message/index.ts
  if (!githubIssueId) {
    // 创建 Issue，并把原始消息回写为 isOriginalIssue=true
    ...
  } else if (
    githubIssueId &&
    state.messages.filter(isHumanMessage).length > 1
  ) {
    // 将未入 Issue 的 Human 消息补充为评论，并在状态中替换为带标记的新消息
    ...
  }
```

- 恢复被中断的 Planner（仅非 Local）：

```282:310:apps/open-swe/src/graphs/manager/nodes/classify-message/index.ts
  if (plannerStatus === "interrupted") {
    ...
    const plannerResume: HumanResponse = { type: "response", args: "resume planner" };
    const newPlannerRun = await langGraphClient.runs.create(
      state.plannerSession?.threadId,
      PLANNER_GRAPH_ID,
      { command: { resume: plannerResume }, streamMode: OPEN_SWE_STREAM_MODE as StreamMode[] },
    );
    newPlannerId = newPlannerRun.run_id;
  }
```

- 返回跳转：
  - `update_programmer`/`update_planner`/`resume_and_update_planner`：补齐评论后直接结束（Planner 路由会在被恢复时更新 runId）。
  - `start_planner`/`start_planner_for_followup`：创建/补齐 Issue 后进入 `start-planner` 节点。

```341:366:apps/open-swe/src/graphs/manager/nodes/classify-message/index.ts
  if (
    (toolCallArgs.route as any) === "update_programmer" ||
    (toolCallArgs.route as any) === "update_planner" ||
    (toolCallArgs.route as any) === "resume_and_update_planner"
  ) {
    return new Command({ update: commandUpdate, goto: END });
  }

  if (
    toolCallArgs.route === "start_planner" ||
    toolCallArgs.route === "start_planner_for_followup"
  ) {
    return new Command({ update: commandUpdate, goto: "start-planner" });
  }
```

### 6) 路由可见性规则（何时会出现在选项里）

- programmer 正在运行：出现 **update_programmer**
- planner 未启动：出现 **start_planner**
- programmer 与 planner 均 idle：出现 **start_planner_for_followup**
- planner 正在运行：出现 **update_planner**
- planner 被中断：出现 **resume_and_update_planner**
- 只要有任一方不是 not_started：出现 **create_new_issue**
- 总是可选：**no_op**

### 7) 重要细节

- 模型输入对用户消息做了“去除 details”的清洗：`extractContentWithoutDetailsFromIssueBody(...)`。
- 写入 Issue 时，首条用户消息会被替换为带 `isOriginalIssue: true` 的 HumanMessage；后续补充评论会标注 `githubIssueCommentId`，若为 follow-up，还会带 `isFollowup: true`。
- `requestSource`（如来自 Web UI 或 Issue 事件）会注入提示词，便于决策。

---

## 各场景示例

为便于理解，以下示例均省略历史消息与系统提示，着重展示“输入上下文 → 期望路由 → 行为”。

### A. 新对话（两者 not_started）
- 输入：用户说「请帮我在项目中添加健康检查接口」
- 期望路由：`start_planner`
- 行为：
  - Local：进入 `start-planner`。
  - 非 Local：先创建 Issue（把原始消息写为 Issue 内容并打 `isOriginalIssue`），然后进入 `start-planner`。

### B. Planner 正在运行（planner=busy）
- 输入：用户补充「健康检查需要返回版本号与构建时间」
- 期望路由：`update_planner`
- 行为：将本次补充写为 Issue 评论，并结束（Planner 运行中会自行拉取上下文）。

### C. Programmer 正在运行（programmer=busy）
- 输入：用户说「把日志级别改为 debug」
- 期望路由：`update_programmer`
- 行为：将补充写为 Issue 评论，并结束（Programmer 会拉取上下文）。

### D. Planner 被中断等待人类（planner=interrupted）
- 输入：用户说「同意你的方案，继续」
- 期望路由：`resume_and_update_planner`
- 行为：调用 `runs.create` 恢复 Planner（以 `HumanResponse: resume planner`），更新 `plannerSession.runId` 并结束。

### E. 两者均 idle（planner=idle, programmer=idle）
- 输入：用户说「再做一个 /metrics 端点」
- 期望路由：`start_planner_for_followup`
- 行为：
  - 若已有 Issue 且存在未写入的用户消息：把该消息写为 Issue 评论，并在回写的人类消息 `additional_kwargs` 中附 `isFollowup: true`。
  - 跳转 `start-planner`，以新任务的跟进方式启动规划。

### F. 创建新会话（已有上下文时）
- 输入：用户说「另开一个 issue 专门处理 CI 缓存问题」
- 期望路由：`create_new_issue`
- 行为：进入 `create-new-session` 节点，基于完整历史启动新 Manager 线程。

### G. 不需要动作（闲聊/致谢）
- 输入：用户说「谢谢你，辛苦了」
- 期望路由：`no_op`
- 行为：仅回复用户并结束，不触发任何规划/执行。

---

## 快速对照表（环境差异）

- Local 模式只允许：`start_planner`、`start_planner_for_followup`；其它路由将报错。
- 非 Local：除上述两项，还会：
  - 自动确保 Issue 存在或补齐评论
  - `update_*` 路由直接 END
  - `resume_and_update_planner` 会恢复 Planner 并 END
  - `create_new_issue` 进入 `create-new-session`


