{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "lint:fix": {"dependsOn": ["^lint:fix"]}, "format": {"dependsOn": ["^format"]}, "format:check": {"dependsOn": ["^format:check"]}, "dev": {"dependsOn": ["^dev"]}, "test": {"dependsOn": ["^test"]}, "clean": {"dependsOn": ["^clean"]}}}