# 技术栈

## 构建系统
- **Monorepo**: 使用 Yarn workspaces 和 Turbo 管理
- **包管理器**: Yarn 3.5.1
- **构建工具**: Turbo (用于并行构建和缓存)

## 核心技术栈
- **运行时**: Node.js 20
- **语言**: TypeScript 5.x
- **框架**: 
  - LangGraph (AI 代理编排)
  - Next.js 15 (Web 应用)
  - Hono (API 服务器)
  - React 19 (前端 UI)

## 主要依赖
- **AI/LLM**: 
  - @langchain/core, @langchain/langgraph
  - @langchain/anthropic, @langchain/openai
  - @langchain/mcp-adapters
- **GitHub 集成**: @octokit/app, @octokit/rest
- **UI 组件**: Radix UI, Tailwind CSS, shadcn/ui
- **状态管理**: Zustand, SWR
- **认证**: jsonwebtoken
- **工具**: zod (验证), uuid, diff

## 常用命令

### 开发
```bash
yarn dev              # 启动所有应用的开发服务器
yarn turbo dev        # 使用 turbo 启动开发环境
```

### 构建和测试
```bash
yarn build            # 构建所有包
yarn test             # 运行所有测试
yarn lint             # 代码检查
yarn lint:fix         # 自动修复 lint 问题
yarn format           # 格式化代码
yarn format:check     # 检查代码格式
```

### 清理
```bash
yarn clean            # 清理构建缓存和输出
```

### LangGraph 特定命令
```bash
# 在 apps/open-swe 目录下
yarn dev              # 启动 LangGraph 开发服务器
yarn eval:single      # 运行单个评估测试
yarn get-trace-urls   # 获取追踪 URL
```

## 开发环境配置
- 需要 `.env` 文件配置 API 密钥和环境变量
- LangGraph 配置在根目录 `langgraph.json`
- 支持本地开发和云端部署