# 产品概述

Open SWE 是一个基于 LangGraph 构建的开源异步编码代理，能够自主理解代码库、规划解决方案并在整个代码仓库中执行代码更改——从初始规划到创建拉取请求。

## 核心功能

- **智能规划**: 专门的规划步骤，深度理解复杂代码库和细致任务
- **人机协作**: 支持在运行过程中发送消息和实时反馈
- **并行执行**: 在云端沙盒环境中并行运行多个任务
- **端到端任务管理**: 自动创建 GitHub issues 和 pull requests

## 使用方式

- **Web UI**: 通过 web 应用创建、管理和执行任务
- **GitHub 集成**: 通过添加 `open-swe`、`open-swe-auto`、`open-swe-max` 等标签直接从 GitHub issues 启动任务

## 架构组件

- **Manager Graph**: 消息分类和会话管理
- **Planner Graph**: 任务规划和上下文分析  
- **Programmer Graph**: 代码实现和错误诊断
- **Reviewer Graph**: 代码审查和质量控制