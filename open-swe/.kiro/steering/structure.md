# 项目结构

## 根目录结构
```
open-swe/
├── apps/                    # 应用程序
├── packages/               # 共享包
├── static/                 # 静态资源
├── langgraph.json         # LangGraph 配置
├── package.json           # 根包配置
├── turbo.json            # Turbo 构建配置
└── yarn.lock             # 依赖锁定文件
```

## 应用程序 (apps/)

### apps/open-swe/ - 核心 AI 代理
LangGraph 驱动的主要代理应用，包含所有 AI 逻辑和图形定义。

**关键目录**:
- `src/graphs/` - LangGraph 图形定义
  - `manager/` - 消息分类和会话管理
  - `planner/` - 任务规划和上下文分析
  - `programmer/` - 代码实现和错误诊断
  - `reviewer/` - 代码审查流程
- `src/tools/` - AI 代理使用的工具
- `src/utils/` - 工具函数和辅助类
- `src/security/` - 认证和安全相关
- `src/routes/` - HTTP 路由处理

### apps/web/ - Web 用户界面
Next.js 应用，提供用户交互界面。

**关键目录**:
- `src/app/` - Next.js 应用路由
- `src/components/` - React 组件
  - `ui/` - 基础 UI 组件 (shadcn/ui)
  - `gen-ui/` - 生成式 UI 组件
  - `github/` - GitHub 集成组件
- `src/hooks/` - React hooks
- `src/lib/` - 工具函数和配置

### apps/cli/ - 命令行界面
基于 Ink 的 CLI 工具，用于终端交互。

### apps/docs/ - 文档站点
项目文档和使用指南。

## 共享包 (packages/)

### packages/shared/
跨应用共享的类型定义、工具函数和常量。

**关键模块**:
- `src/open-swe/` - Open SWE 特定类型和工具
- `src/github/` - GitHub 集成相关
- `src/constants.ts` - 全局常量
- `src/messages.ts` - 消息类型定义

## 命名约定

### 文件命名
- 组件文件: PascalCase (如 `TaskList.tsx`)
- 工具文件: kebab-case (如 `github-api.ts`)
- 类型文件: kebab-case (如 `open-swe-types.ts`)

### 目录结构
- 按功能分组，而非按文件类型
- 使用 `index.ts` 文件作为模块入口
- 测试文件放在 `__tests__/` 目录或与源文件同级

### 导入约定
- 使用相对路径导入同级或子级模块
- 使用包名导入跨包模块 (如 `@open-swe/shared`)
- 按字母顺序组织导入语句

## 配置文件位置
- TypeScript: 各应用有独立的 `tsconfig.json`
- ESLint: 各应用有独立的 `eslint.config.js`
- 环境变量: 各应用有独立的 `.env` 文件