# ------------------<PERSON><PERSON><PERSON> tracing------------------
LANGCHAIN_PROJECT="default"
LANGCHAIN_API_KEY="lsv2_pt_..."
LANGCHAIN_TRACING_V2="true"
# Set to true when ready to run evals, _and_ have the results uploaded to LangSmith.
# If false, evals will still run, but results will not be saved in LangSmith.
LANGCHAIN_TEST_TRACKING="false"


# ------------------LLM Provider Keys------------------
# Defaults to Anthropic models.
ANTHROPIC_API_KEY=""
OPENAI_API_KEY=""
GOOGLE_API_KEY=""


# ------------------Infrastructure---------------------
# Daytona API key for creating & accessing the cloud sandbox.
DAYTONA_API_KEY=""


# ------------------------Tools------------------------
# Firecrawl API key for calling the get URL contents tool.
FIRECRAWL_API_KEY=""


# ------------------Github App Secrets-----------------
GITHUB_APP_NAME="open-swe-dev" # this must match the name of your GitHub app, excluding spaces
GITHUB_APP_ID=""
# App secret key. Should be multi-line.
GITHUB_APP_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----
...add your private key here...
-----END RSA PRIVATE KEY-----
"
# Secret key for verifying GitHub webhook events.
GITHUB_WEBHOOK_SECRET=""


# ------------------------Other------------------------
# Defaults to 2024 if not set.
# LGP will automatically set this for you in production.
PORT="2024"
# Used to create a run URL when replying to a GitHub issue comment.
# Should be the URL of the web app. Localhost in dev, production URL
# in production.
OPEN_SWE_APP_URL="http://localhost:3000"
# Encryption key for secrets (32-byte hex string for AES-256)
# Should be the same value as the one used in the web app, so that secrets
# encrypted in the web app can be decrypted in the agent.
SECRETS_ENCRYPTION_KEY=""
# Whether or not to append the string "[skip ci]" to the commit message.
# See the documentation for how to set this up: docs.langchain.com/labs/swe/setup/ci#skip-ci-until-last-commit
SKIP_CI_UNTIL_LAST_COMMIT="true"

# For the CLI to work, you need to set these variables.
# OPEN_SWE_LOCAL_MODE=false
# OPEN_SWE_LOCAL_PROJECT_PATH=""
