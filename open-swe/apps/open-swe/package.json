{"name": "@open-swe/agent", "homepage": "https://github.com/langchain-ai/open-swe/blob/main/README.md", "repository": {"type": "git", "url": "https://github.com/langchain-ai/open-swe.git"}, "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "langgraphjs dev --no-browser --config ../../langgraph.json", "clean": "rm -rf .turbo ../../.langgraph_api ./dist || true", "build": "tsc", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "test": "NODE_OPTIONS=--experimental-vm-modules yarn run jest --config jest.config.js --testPathIgnorePatterns=int.test.ts", "test:int": "node --experimental-vm-modules node_modules/jest/bin/jest.js --config jest.config.js --testPathPattern=int.test.ts", "test:single": "NODE_OPTIONS=--experimental-vm-modules yarn run jest --config jest.config.js --testTimeout 100000", "eval:single": "NODE_OPTIONS=--experimental-vm-modules yarn run vitest --config ls.vitest.config.ts --run", "get-trace-urls": "tsx scripts/get-trace-urls.ts", "postinstall": "turbo build"}, "dependencies": {"@daytonaio/sdk": "^0.25.5", "@langchain/anthropic": "^0.3.26", "@langchain/community": "^0.3.47", "@langchain/core": "^0.3.65", "@langchain/google-genai": "^0.2.9", "@langchain/langgraph": "^0.3.8", "@langchain/langgraph-sdk": "^0.0.95", "@langchain/mcp-adapters": "^0.5.2", "@langchain/openai": "^0.5.10", "@mendable/firecrawl-js": "^1.29.1", "@octokit/app": "^16.0.1", "@octokit/core": "^7.0.2", "@octokit/rest": "^22.0.0", "@octokit/webhooks": "^14.0.2", "@open-swe/shared": "*", "bcrypt": "^6.0.0", "diff": "^8.0.1", "hono": "^4.8.3", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.26", "langsmith": "^0.3.29", "uuid": "^11.0.5", "zod": "^3.25.32"}, "devDependencies": {"@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.19.0", "@jest/globals": "^29.7.0", "@langchain/langgraph-cli": "^0.0.47", "@tsconfig/recommended": "^1.0.8", "@types/bcrypt": "^6.0.0", "@types/commander": "^2.12.5", "@types/jest": "^29.5.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.13.5", "commander": "^14.0.0", "dotenv": "^16.4.7", "eslint": "^9.19.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.7.0", "prettier": "^3.5.2", "ts-jest": "^29.1.0", "tsx": "^4.20.3", "turbo": "^2.5.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vitest": "^3.2.3"}, "packageManager": "yarn@3.5.1", "description": "The core LangGraph agent application that powers Open SWE's autonomous code understanding, planning, and execution capabilities.", "license": "MIT", "bugs": {"url": "https://github.com/langchain-ai/open-swe/issues"}}