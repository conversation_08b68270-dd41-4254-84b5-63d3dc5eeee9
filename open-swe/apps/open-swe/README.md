# Open SWE Agent

The core LangGraph agent application that powers Open SWE's autonomous code understanding, planning, and execution capabilities.

## Documentation

For detailed setup and usage information, see the [development setup documentation](https://docs.langchain.com/labs/swe/setup/development).

## Development

1. Copy the environment file: `cp .env.example .env` and fill in the required values
2. Install dependencies: `yarn install`
3. Start the development server: `yarn dev`
