import {
  GraphConfig,
  TargetRepository,
  TaskPlan,
} from "@open-swe/shared/open-swe/types";
import { getIssue, updateIssue } from "./api.js";
import { getGitHubTokensFromConfig } from "../github-tokens.js";
import { createLogger, LogLevel } from "../logger.js";
import { isLocalMode } from "@open-swe/shared/open-swe/local-mode";
const logger = createLogger(LogLevel.INFO, "IssueTaskString");

/**
 * 这些特殊标签用于在 GitHub Issue 的正文中标记并存储结构化的计划信息。
 * 我们以 JSON 字符串的形式把计划包裹在这些标签之间，从而实现「无损写回/解析」。
 *
 * 在真实的 Issue 正文中，大致会是这样的片段：
 *
 * ```markdown
 * <details>
 * <summary>Agent Context</summary>
 * <open-swe-do-not-edit-task-plan>
 * {"tasks": [...], "activeTaskIndex": 0}
 * </open-swe-do-not-edit-task-plan>
 * </details>
 * ```
 */
export const TASK_OPEN_TAG = "<open-swe-do-not-edit-task-plan>";
export const TASK_CLOSE_TAG = "</open-swe-do-not-edit-task-plan>";

export const PROPOSED_PLAN_OPEN_TAG = "<open-swe-do-not-edit-proposed-plan>";
export const PROPOSED_PLAN_CLOSE_TAG = "</open-swe-do-not-edit-proposed-plan>";

export const DETAILS_OPEN_TAG = "<details>";
export const DETAILS_CLOSE_TAG = "</details>";
const AGENT_CONTEXT_DETAILS_SUMMARY = "<summary>Agent Context</summary>";

/**
 * 类型守卫：在从 Issue 文本解析出未知对象后，校验其是否符合 TaskPlan 结构。
 *
 * - 要求对象上存在 `tasks: Task[]` 与 `activeTaskIndex: number` 两个关键字段。
 * - 仅用于运行时窄化类型，防止后续访问属性时报错。
 */
function typeNarrowTaskPlan(taskPlan: unknown): taskPlan is TaskPlan {
  return !!(
    typeof taskPlan === "object" &&
    !Array.isArray(taskPlan) &&
    taskPlan &&
    "tasks" in taskPlan &&
    Array.isArray(taskPlan.tasks) &&
    "activeTaskIndex" in taskPlan &&
    typeof taskPlan.activeTaskIndex === "number"
  );
}

/**
 * 从 Issue 正文中提取任务计划 TaskPlan（位于 `<open-swe-do-not-edit-task-plan>` 标签之间）。
 *
 * 工作流程：
 * - 先检查开闭标签是否存在；
 * - 截取标签之间的文本；
 * - 尝试以 JSON 解析并用类型守卫校验结构；
 * - 若解析失败或结构不符，返回 null 并记录日志。
 *
 * 示例（Issue 正文片段）：
 * ```markdown
 * ...任意正文...
 * <details>
 * <summary>Agent Context</summary>
 * <open-swe-do-not-edit-task-plan>
 * {
 *   "tasks": [
 *     {
 *       "id": "task-1",
 *       "taskIndex": 1,
 *       "request": "实现登录接口",
 *       "title": "实现基础登录",
 *       "createdAt": 1710000000000,
 *       "completed": false,
 *       "planRevisions": [],
 *       "activeRevisionIndex": 0
 *     }
 *   ],
 *   "activeTaskIndex": 0
 * }
 * </open-swe-do-not-edit-task-plan>
 * </details>
 * ```
 *
 * 使用示例：
 * ```ts
 * const plan = extractTasksFromIssueContent(issue.body ?? "");
 * if (plan) {
 *   // plan.tasks[plan.activeTaskIndex] 即为当前激活任务
 * }
 * ```
 *
 * @param content Issue 正文字符串
 * @returns 解析成功返回 TaskPlan，否则返回 null
 */
export function extractTasksFromIssueContent(content: string): TaskPlan | null {
  if (!content.includes(TASK_OPEN_TAG) || !content.includes(TASK_CLOSE_TAG)) {
    return null;
  }
  const taskPlanString = content
    .split(TASK_OPEN_TAG)?.[1]
    ?.split(TASK_CLOSE_TAG)?.[0];
  try {
    const parsedTaskPlan = JSON.parse(taskPlanString.trim());
    if (!typeNarrowTaskPlan(parsedTaskPlan)) {
      throw new Error("Invalid task plan parsed.");
    }
    return parsedTaskPlan;
  } catch (e) {
    logger.error("Failed to parse task plan", {
      taskPlanString,
      ...(e instanceof Error && {
        name: e.name,
        message: e.message,
        stack: e.stack,
      }),
    });
    return null;
  }
}

/**
 * 从 Issue 正文中提取「候选方案」 Proposed Plan（位于 `<open-swe-do-not-edit-proposed-plan>` 标签之间）。
 *
 * 该字段通常是一个字符串数组，代表规划阶段给用户审核的步骤/想法清单。
 *
 * 示例（Issue 正文片段）：
 * ```markdown
 * <open-swe-do-not-edit-proposed-plan>
 * [
 *   "阅读现有登录模块代码，确认可复用接口",
 *   "在 apps/open-swe/src/routes/… 下新增登录路由",
 *   "补充单元测试与错误处理"
 * ]
 * </open-swe-do-not-edit-proposed-plan>
 * ```
 *
 * @param content Issue 正文字符串
 * @returns string[] | null 解析成功返回字符串数组，否则返回 null
 */
function extractProposedPlanFromIssueContent(content: string): string[] | null {
  if (
    !content.includes(PROPOSED_PLAN_OPEN_TAG) ||
    !content.includes(PROPOSED_PLAN_CLOSE_TAG)
  ) {
    return null;
  }
  const proposedPlanString = content
    .split(PROPOSED_PLAN_OPEN_TAG)?.[1]
    ?.split(PROPOSED_PLAN_CLOSE_TAG)?.[0];
  try {
    const parsedProposedPlan = JSON.parse(proposedPlanString.trim());
    return parsedProposedPlan;
  } catch (e) {
    logger.error("Failed to parse proposed plan", {
      proposedPlanString,
      ...(e instanceof Error && {
        name: e.name,
        message: e.message,
        stack: e.stack,
      }),
    });
    return null;
  }
}

type GetIssueTaskPlanInput = {
  githubIssueId: number;
  targetRepository: TargetRepository;
};

/**
 * 读取指定仓库/编号的 GitHub Issue，并解析其中的 TaskPlan 与 ProposedPlan。
 *
 * 特殊行为：
 * - 若当前处于本地模式（Local Mode），直接返回 `{ taskPlan: null, proposedPlan: null }`，避免外部 API 访问。
 * - 否则通过 GitHub API `getIssue` 获取 Issue 正文，再分别用解析函数提取内容。
 *
 * 使用示例：
 * ```ts
 * const { taskPlan, proposedPlan } = await getPlansFromIssue(
 *   { githubIssueId: 123, targetRepository: { owner: "acme", repo: "demo" } },
 *   config,
 * );
 * if (taskPlan) {
 *   // 可在后续执行阶段使用 taskPlan
 * }
 * if (proposedPlan?.length) {
 *   // 在 UI 上展示候选方案，供用户接受/修改
 * }
 * ```
 */
export async function getPlansFromIssue(
  input: GetIssueTaskPlanInput,
  config: GraphConfig,
): Promise<{
  taskPlan: TaskPlan | null;
  proposedPlan: string[] | null;
}> {
  if (isLocalMode(config)) {
    return {
      taskPlan: null,
      proposedPlan: null,
    };
  }
  const issue = await getIssue({
    owner: input.targetRepository.owner,
    repo: input.targetRepository.repo,
    issueNumber: input.githubIssueId,
    githubInstallationToken:
      getGitHubTokensFromConfig(config).githubInstallationToken,
  });
  if (!issue || !issue.body) {
    throw new Error(
      "No issue found when attempting to get task plan from issue",
    );
  }

  const taskPlan = extractTasksFromIssueContent(issue.body);
  const proposedPlan = extractProposedPlanFromIssueContent(issue.body);
  return {
    taskPlan,
    proposedPlan,
  };
}

/**
 * 将给定的计划字符串（JSON）插入或更新到 Issue 正文中。
 *
 * 行为描述：
 * - 若正文中不存在对应的计划标签，则：
 *   - 若不存在 `<details>` 区块：创建一个包含 `<summary>Agent Context</summary>` 的 details 区块，
 *     并把计划放进去；
 *   - 若已存在 `<details>`：优先把计划插入到该 details 的 `summary` 后；
 * - 若已存在计划标签：用新内容整体替换掉标签中的旧内容（保持标签外的正文不变）。
 *
 * 示例：
 * ```ts
 * // 传入 proposedPlan 的字符串化结果
 * const body = insertPlanToIssueBody(issue.body, JSON.stringify(["step1"], null, 2), "proposedPlan");
 * ```
 */
function insertPlanToIssueBody(
  issueBody: string,
  planString: string,
  planType: "taskPlan" | "proposedPlan",
) {
  const openingPlanTag =
    planType === "taskPlan" ? TASK_OPEN_TAG : PROPOSED_PLAN_OPEN_TAG;
  const closingPlanTag =
    planType === "taskPlan" ? TASK_CLOSE_TAG : PROPOSED_PLAN_CLOSE_TAG;

  const wrappedPlan = `${openingPlanTag}
${planString}
${closingPlanTag}`;

  if (
    !issueBody.includes(openingPlanTag) &&
    !issueBody.includes(closingPlanTag)
  ) {
    if (
      !issueBody.includes(DETAILS_OPEN_TAG) &&
      !issueBody.includes(DETAILS_CLOSE_TAG)
    ) {
      return `${issueBody}
${DETAILS_OPEN_TAG}
${AGENT_CONTEXT_DETAILS_SUMMARY}
${wrappedPlan}
${DETAILS_CLOSE_TAG}`;
    } else {
      // No plan present yet, but details already exists.
      const contentBeforeDetailsTag = issueBody.split(DETAILS_OPEN_TAG)?.[0];
      const contentAfterDetailsOpenTag =
        issueBody.split(DETAILS_OPEN_TAG)?.[1] || "";
      const contentAfterSummary = contentAfterDetailsOpenTag.includes(
        AGENT_CONTEXT_DETAILS_SUMMARY,
      )
        ? contentAfterDetailsOpenTag.split(AGENT_CONTEXT_DETAILS_SUMMARY)[1]
        : contentAfterDetailsOpenTag;
      const contentAfterDetailsCloseTag =
        issueBody.split(DETAILS_CLOSE_TAG)?.[1] || "";

      return `${contentBeforeDetailsTag}${DETAILS_OPEN_TAG}
${AGENT_CONTEXT_DETAILS_SUMMARY}
${wrappedPlan}${
        contentAfterSummary.trim()
          ? `
${contentAfterSummary.trim()}`
          : ""
      }
${DETAILS_CLOSE_TAG}${contentAfterDetailsCloseTag}`;
    }
  } else {
    const contentBeforeOpenTag = issueBody.split(openingPlanTag)?.[0];
    const contentAfterCloseTag = issueBody.split(closingPlanTag)?.[1];

    return `${contentBeforeOpenTag}
${wrappedPlan}
${contentAfterCloseTag}`;
  }
}

/**
 * 把「候选方案」写回到指定 Issue 的正文中（以 JSON 文本 + 标签包裹）。
 *
 * - 内部会先获取 Issue 正文，调用 `insertPlanToIssueBody` 合成新的正文，再通过 `updateIssue` 提交更新；
 * - 更新策略为「覆盖标签之间的旧内容」。
 *
 * 示例：
 * ```ts
 * await addProposedPlanToIssue(
 *   { githubIssueId: 123, targetRepository: { owner: "acme", repo: "demo" } },
 *   config,
 *   [
 *     "分析现有登录流程",
 *     "实现登录路由与 Controller",
 *     "编写单元测试并补充错误处理"
 *   ],
 * );
 * ```
 */
export async function addProposedPlanToIssue(
  input: GetIssueTaskPlanInput,
  config: GraphConfig,
  proposedPlan: string[],
) {
  const issue = await getIssue({
    owner: input.targetRepository.owner,
    repo: input.targetRepository.repo,
    issueNumber: input.githubIssueId,
    githubInstallationToken:
      getGitHubTokensFromConfig(config).githubInstallationToken,
  });
  if (!issue || !issue.body) {
    throw new Error(
      "No issue found when attempting to get task plan from issue",
    );
  }

  const proposedPlanString = JSON.stringify(proposedPlan, null, 2);
  const newBody = insertPlanToIssueBody(
    issue.body,
    proposedPlanString,
    "proposedPlan",
  );

  await updateIssue({
    owner: input.targetRepository.owner,
    repo: input.targetRepository.repo,
    issueNumber: input.githubIssueId,
    githubInstallationToken:
      getGitHubTokensFromConfig(config).githubInstallationToken,
    body: newBody,
  });
}

/**
 * 把 TaskPlan（任务计划，包含任务列表与当前激活索引）写回到指定 Issue 的正文中。
 *
 * 常见用法：当用户在 UI 端「接受计划」后，我们会将系统内的结构化 TaskPlan 落盘到 Issue，
 * 以便后续任何时间都可以从 Issue 恢复上下文（支持跨进程/跨服务）。
 *
 * 示例：
 * ```ts
 * const taskPlan: TaskPlan = {
 *   tasks: [
 *     {
 *       id: "task-1",
 *       taskIndex: 1,
 *       request: "实现登录接口",
 *       title: "实现基础登录",
 *       createdAt: Date.now(),
 *       completed: false,
 *       planRevisions: [],
 *       activeRevisionIndex: 0
 *     }
 *   ],
 *   activeTaskIndex: 0,
 * };
 * await addTaskPlanToIssue(
 *   { githubIssueId: 123, targetRepository: { owner: "acme", repo: "demo" } },
 *   config,
 *   taskPlan,
 * );
 * ```
 */
export async function addTaskPlanToIssue(
  input: GetIssueTaskPlanInput,
  config: GraphConfig,
  taskPlan: TaskPlan,
): Promise<void> {
  const issue = await getIssue({
    owner: input.targetRepository.owner,
    repo: input.targetRepository.repo,
    issueNumber: input.githubIssueId,
    githubInstallationToken:
      getGitHubTokensFromConfig(config).githubInstallationToken,
  });

  if (!issue || !issue.body) {
    throw new Error("No issue found when attempting to add task plan to issue");
  }

  const taskPlanString = JSON.stringify(taskPlan, null, 2);
  const newBody = insertPlanToIssueBody(issue.body, taskPlanString, "taskPlan");

  await updateIssue({
    owner: input.targetRepository.owner,
    repo: input.targetRepository.repo,
    issueNumber: input.githubIssueId,
    githubInstallationToken:
      getGitHubTokensFromConfig(config).githubInstallationToken,
    body: newBody,
  });
}
