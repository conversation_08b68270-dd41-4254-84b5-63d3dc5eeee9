import { v4 as uuidv4 } from "uuid";
import {
  BaseMessage,
  HumanMessage,
  isHumanMessage,
} from "@langchain/core/messages";
import { GitHubIssue, GitHubIssueComment } from "./types.js";
import { getIssue, getIssueComments } from "./api.js";
import { GraphConfig, TargetRepository } from "@open-swe/shared/open-swe/types";
import { getGitHubTokensFromConfig } from "../github-tokens.js";
import { DETAILS_OPEN_TAG } from "./issue-task.js";
import { isLocalMode } from "@open-swe/shared/open-swe/local-mode";

/**
 * 从 GitHub 评论生成尚未被系统追踪的 HumanMessage 列表。
 *
 * 逻辑说明：
 * - 仅针对用户评论（HumanMessage），并排除原始 Issue 消息（`isOriginalIssue`）。
 * - 对于每条 GitHub 评论，若在现有消息中找不到 `additional_kwargs.githubIssueCommentId === comment.id`，
 *   则创建一条新的 `HumanMessage`：
 *   - `id`: 使用 uuidv4 生成
 *   - `content`: 由 `getMessageContentFromIssue` 生成，形如 `[issue comment]\n<评论内容>`
 *   - `additional_kwargs`: 包含 `githubIssueId` 与 `githubIssueCommentId`
 *
 * 使用示例：
 * ```ts
 * const existingMessages = [
 *   new HumanMessage({
 *     id: '1',
 *     content: '[original issue]\n**登录功能**\n...',
 *     additional_kwargs: { githubIssueId: 123, isOriginalIssue: true },
 *   }),
 * ];
 * const comments = [
 *   { id: 10, body: '请同时支持 OAuth 登录', user: { login: 'alice' } } as GitHubIssueComment,
 * ];
 * const msgs = getUntrackedComments(existingMessages, 123, comments);
 * // msgs 将包含一条新的 HumanMessage，content 为 '[issue comment]\n请同时支持 OAuth 登录'
 * ```
 */
export function getUntrackedComments(
  existingMessages: BaseMessage[],
  githubIssueId: number,
  comments: GitHubIssueComment[],
): BaseMessage[] {
  // Get all human messages which contain github comment content. Exclude the original issue message.
  const humanMessages = existingMessages.filter(
    (m) => isHumanMessage(m) && !m.additional_kwargs?.isOriginalIssue,
  );
  // Iterate over the comments, and filter out any comment already tracked by a message.
  // Then, map to create new human message(s).
  const untrackedCommentMessages = comments
    .filter(
      (c) =>
        !humanMessages.some(
          (m) => m.additional_kwargs?.githubIssueCommentId === c.id,
        ),
    )
    .map(
      (c) =>
        new HumanMessage({
          id: uuidv4(),
          content: getMessageContentFromIssue(c),
          additional_kwargs: {
            githubIssueId,
            githubIssueCommentId: c.id,
          },
        }),
    );

  return untrackedCommentMessages;
}

type GetMissingMessagesInput = {
  messages: BaseMessage[];
  githubIssueId: number;
  targetRepository: TargetRepository;
};

export async function getMissingMessages(
  input: GetMissingMessagesInput,
  config: GraphConfig,
): Promise<BaseMessage[]> {
  if (isLocalMode(config)) {
    return [];
  }

  const { githubInstallationToken } = getGitHubTokensFromConfig(config);
  const [issue, comments] = await Promise.all([
    getIssue({
      owner: input.targetRepository.owner,
      repo: input.targetRepository.repo,
      issueNumber: input.githubIssueId,
      githubInstallationToken,
    }),
    getIssueComments({
      owner: input.targetRepository.owner,
      repo: input.targetRepository.repo,
      issueNumber: input.githubIssueId,
      githubInstallationToken,
      filterBotComments: true,
    }),
  ]);
  if (!issue && !comments?.length) {
    return [];
  }

  const isIssueMessageTracked = issue
    ? input.messages.some(
        (m) =>
          isHumanMessage(m) &&
          m.additional_kwargs?.isOriginalIssue &&
          m.additional_kwargs?.githubIssueId === input.githubIssueId,
      )
    : false;
  let issueMessage: HumanMessage | null = null;
  if (issue && !isIssueMessageTracked) {
    issueMessage = new HumanMessage({
      id: uuidv4(),
      content: getMessageContentFromIssue(issue),
      additional_kwargs: {
        githubIssueId: input.githubIssueId,
        isOriginalIssue: true,
      },
    });
  }

  const untrackedCommentMessages = comments?.length
    ? getUntrackedComments(input.messages, input.githubIssueId, comments)
    : [];

  return [...(issueMessage ? [issueMessage] : []), ...untrackedCommentMessages];
}

/** 默认 Issue 标题（当未从消息中提取到自定义标题时使用）。 */
export const DEFAULT_ISSUE_TITLE = "New Open SWE Request";
/**
 * 以下特殊标签用于在消息—Issue 的转换过程中，显式嵌入/提取标题与正文内容。
 *
 * 典型消息格式（供创建 Issue 使用）：
 * ```md
 * <open-swe-issue-title>登录功能需求</open-swe-issue-title>
 * <open-swe-issue-content>请实现用户名密码登录，并返回用户信息</open-swe-issue-content>
 * ```
 */
export const ISSUE_TITLE_OPEN_TAG = "<open-swe-issue-title>";
export const ISSUE_TITLE_CLOSE_TAG = "</open-swe-issue-title>";
export const ISSUE_CONTENT_OPEN_TAG = "<open-swe-issue-content>";
export const ISSUE_CONTENT_CLOSE_TAG = "</open-swe-issue-content>";

/**
 * 从一段消息内容中提取 Issue 的标题与正文（通过自定义标签）。
 *
 * 行为：
 * - 若存在 `<open-swe-issue-title>` 包裹的内容，则解析为 `title`；否则 `title = null`；
 * - 若存在 `<open-swe-issue-content>` 包裹的内容，则解析为 `content`；否则 `content = 原始输入`。
 *
 * 示例：
 * ```ts
 * const raw = '<open-swe-issue-title>实现登录</open-swe-issue-title>' +
 *             '<open-swe-issue-content>请实现登录接口</open-swe-issue-content>';
 * const { title, content } = extractIssueTitleAndContentFromMessage(raw);
 * // title: '实现登录'
 * // content: '请实现登录接口'
 * ```
 */
export function extractIssueTitleAndContentFromMessage(content: string) {
  let messageTitle: string | null = null;
  let messageContent = content;
  if (
    content.includes(ISSUE_TITLE_OPEN_TAG) &&
    content.includes(ISSUE_TITLE_CLOSE_TAG)
  ) {
    messageTitle = content.substring(
      content.indexOf(ISSUE_TITLE_OPEN_TAG) + ISSUE_TITLE_OPEN_TAG.length,
      content.indexOf(ISSUE_TITLE_CLOSE_TAG),
    );
  }
  if (
    content.includes(ISSUE_CONTENT_OPEN_TAG) &&
    content.includes(ISSUE_CONTENT_CLOSE_TAG)
  ) {
    messageContent = content.substring(
      content.indexOf(ISSUE_CONTENT_OPEN_TAG) + ISSUE_CONTENT_OPEN_TAG.length,
      content.indexOf(ISSUE_CONTENT_CLOSE_TAG),
    );
  }
  return { title: messageTitle, content: messageContent };
}

/**
 * 将正文包裹在 `<open-swe-issue-content>` 标签中，便于后续可靠解析。
 *
 * 示例：
 * ```ts
 * const body = formatContentForIssueBody('请实现登录接口');
 * // => '<open-swe-issue-content>请实现登录接口</open-swe-issue-content>'
 * ```
 */
export function formatContentForIssueBody(body: string): string {
  return `${ISSUE_CONTENT_OPEN_TAG}${body}${ISSUE_CONTENT_CLOSE_TAG}`;
}

/**
 * 从 Issue 正文中提取被 `<open-swe-issue-content>` 包裹的内容；
 * 若不存在这些标签，则返回原始正文。
 */
function extractContentFromIssueBody(body: string): string {
  if (
    !body.includes(ISSUE_CONTENT_OPEN_TAG) ||
    !body.includes(ISSUE_CONTENT_CLOSE_TAG)
  ) {
    return body;
  }

  return body.substring(
    body.indexOf(ISSUE_CONTENT_OPEN_TAG) + ISSUE_CONTENT_OPEN_TAG.length,
    body.indexOf(ISSUE_CONTENT_CLOSE_TAG),
  );
}

/**
 * 提取 Issue 正文中不包含 `<details>` 区块的主要内容。
 *
 * 背景：我们的系统会把结构化上下文（如计划 JSON）放入 details 折叠块中，
 * 展示给人类，但在生成消息输入给 Agent 时通常只需要「正文」部分。
 *
 * 实现：
 * - 以 `<details>` 作为分割点，保留其之前的正文，再调用 `extractContentFromIssueBody` 解析内容标签。
 */
export function extractContentWithoutDetailsFromIssueBody(
  body: string,
): string {
  if (!body.includes(DETAILS_OPEN_TAG)) {
    return extractContentFromIssueBody(body);
  }

  const bodyWithoutDetails = extractContentFromIssueBody(
    body.split(DETAILS_OPEN_TAG)[0],
  );
  return bodyWithoutDetails;
}

/**
 * 将 GitHub Issue 或评论转换为内部使用的消息文本。
 *
 * - 对 Issue：前缀标识为 `[original issue]`，标题以粗体展示，正文为去除 details 的主要内容；
 * - 对 评论：前缀标识为 `[issue comment]`，正文为评论文本。
 *
 * 示例：
 * ```ts
 * const issue = { title: '实现登录', body: '<open-swe-issue-content>请实现登录</open-swe-issue-content>' } as GitHubIssue;
 * const text1 = getMessageContentFromIssue(issue);
 * // => '[original issue]\n**实现登录**\n请实现登录'
 *
 * const comment = { id: 10, body: '需要支持错误码返回' } as GitHubIssueComment;
 * const text2 = getMessageContentFromIssue(comment);
 * // => '[issue comment]\n需要支持错误码返回'
 * ```
 */
export function getMessageContentFromIssue(
  issue: GitHubIssue | GitHubIssueComment,
): string {
  if ("title" in issue) {
    return `[original issue]\n**${issue.title}**\n${extractContentFromIssueBody(issue.body ?? "")}`;
  }
  return `[issue comment]\n${issue.body}`;
}
