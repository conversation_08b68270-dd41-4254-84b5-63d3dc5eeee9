import { GraphConfig } from "@open-swe/shared/open-swe/types";
import {
  ManagerGraphState,
  ManagerGraphUpdate,
} from "@open-swe/shared/open-swe/manager/types";
import { createLangGraphClient } from "../../../../utils/langgraph-client.js";
import {
  BaseMessage,
  HumanMessage,
  isHumanMessage,
  RemoveMessage,
} from "@langchain/core/messages";
import { z } from "zod";
import {
  loadModel,
  supportsParallelToolCallsParam,
} from "../../../../utils/llms/index.js";
import { LLMTask } from "@open-swe/shared/open-swe/llm-task";
import { Command, END } from "@langchain/langgraph";
import { getMessageContentString } from "@open-swe/shared/messages";
import {
  createIssue,
  createIssueComment,
} from "../../../../utils/github/api.js";
import { getGitHubTokensFromConfig } from "../../../../utils/github-tokens.js";
import { createIssueFieldsFromMessages } from "../../utils/generate-issue-fields.js";
import {
  extractContentWithoutDetailsFromIssueBody,
  extractIssueTitleAndContentFromMessage,
  formatContentForIssueBody,
} from "../../../../utils/github/issue-messages.js";
import { getDefaultHeaders } from "../../../../utils/default-headers.js";
import { BASE_CLASSIFICATION_SCHEMA } from "./schemas.js";
import { getPlansFromIssue } from "../../../../utils/github/issue-task.js";
import { HumanResponse } from "@langchain/langgraph/prebuilt";
import {
  OPEN_SWE_STREAM_MODE,
  PLANNER_GRAPH_ID,
} from "@open-swe/shared/constants";
import { createLogger, LogLevel } from "../../../../utils/logger.js";
import { createClassificationPromptAndToolSchema } from "./utils.js";
import { RequestSource } from "../../../../constants.js";
import { StreamMode, Thread } from "@langchain/langgraph-sdk";
import { isLocalMode } from "@open-swe/shared/open-swe/local-mode";
import { PlannerGraphState } from "@open-swe/shared/open-swe/planner/types";
import { GraphState } from "@open-swe/shared/open-swe/types";
import { Client } from "@langchain/langgraph-sdk";
const logger = createLogger(LogLevel.INFO, "ClassifyMessage");

/**
 * 对最新的人类消息进行分类，并产出下一步的路由与状态更新（Command）。
 *
 * 核心职责：
 * - 读取上下文（是否 Local Mode、是否已有 Planner/Programmer 会话、Issue 中的最新任务计划等）；
 * - 构建分类提示词与工具模式，调用路由模型，并读取工具调用结果（route + args）；
 * - 根据分类结果与上下文，决定是否创建 GitHub Issue、补充缺失评论、恢复/启动 Planner，或直接结束（no_op）。
 *
 * 主要流程：
 * 1) 取最后一条 HumanMessage，若不存在则抛错。
 * 2) 非本地模式下：创建 LangGraph Client，尝试加载 Planner/Programmer 的线程状态（用于判断是否在运行/中断）。
 * 3) 若存在 `githubIssueId`，从 Issue 解析最新的 `taskPlan` 与 `proposedPlan`（优先使用远端事实）。
 * 4) 调用路由模型：
 *    - 以系统消息注入 prompt（含 Planner/Programmer 状态、消息历史、taskPlan/proposedPlan 等），
 *    - 以用户消息注入正文文本（去掉 details 与结构化标签）。
 *    - 工具固定为 `respond_and_route`，强制选择该工具；
 * 5) 读取工具调用参数 `toolCallArgs`（符合 `BASE_CLASSIFICATION_SCHEMA`）：
 *    - route: 决定走向（no_op / create_new_issue / start_planner / start_planner_for_followup / update_* 等）；
 *
 * 路由分支：
 * - no_op：将模型回复追加进状态并结束（END）。
 * - create_new_issue：将消息历史生成一个新 Issue 的标题/正文，创建 Issue；把用户原始消息替换为带有 `githubIssueId` 与 `isOriginalIssue` 的版本；跳转到 `create-new-session`。
 * - Local Mode：
 *   - 仅追加模型回复，不做任何 GitHub 读写；
 *   - 若 route ∈ {start_planner, start_planner_for_followup} → 跳转 `start-planner`；否则报错（本地模式不支持）。
 * - 远端模式且已存在 GitHub Issue：
 *   - 若有多条 HumanMessage 且部分尚未写入 Issue，则为这些消息创建 Issue 评论，随后：
 *     - 若 Planner 处于 `interrupted`，通过 `runs.create(..., resume: 'resume planner')` 恢复 Planner；
 *     - 若 route === `start_planner_for_followup`，将 goto 设为 `start-planner`；
 *     - 返回包含新消息（替换为带 `githubIssueId`/`githubIssueCommentId` 等标记）的更新；
 * - 其余：
 *   - 若 route ∈ {update_programmer, update_planner, resume_and_update_planner}，直接返回 END（程序员/规划器会在后续拉取 Issue 变化）；
 *   - 若 route ∈ {start_planner, start_planner_for_followup}，跳转 `start-planner`；
 *   - 否则报错（无效路由）。
 *
 * 错误与边界：
 * - 缺少 HumanMessage 直接抛错；
 * - 在需要创建/读取 Issue 时，若 GitHub API 失败，会抛错；
 * - 本地模式下不允许需要 GitHub 的路由（除启动 Planner）。
 *
 * 典型场景：
 * - 场景 A：首次收到请求、还未创建 Issue（远端模式）
 *   ```ts
 *   // 输入：state.githubIssueId = undefined, route = 'start_planner'
 *   // 行为：创建 Issue（标题由历史消息自动生成，正文为用户消息提取内容），
 *   //       用 RemoveMessage + 带 { githubIssueId, isOriginalIssue: true } 的 HumanMessage 替换原始消息，
 *   //       返回 Command，goto: 'start-planner'。
 *   ```
 * - 场景 B：已有 Issue，用户追加多条消息（远端模式）
 *   ```ts
 *   // 输入：state.githubIssueId = 123，messages 中有多条 HumanMessage，其中部分没有 additional_kwargs.githubIssueId
 *   // 行为：仅为缺失的消息创建 Issue 评论，并用带 { githubIssueId, githubIssueCommentId } 的 HumanMessage 替换，
 *   //       若 plannerStatus === 'interrupted' 则恢复 Planner；
 *   //       若 route === 'start_planner_for_followup' 则 goto: 'start-planner'；
 *   //       返回更新后的消息列表与可能更新的 plannerSession。
 *   ```
 * - 场景 C：Local Mode 开发/调试
 *   ```ts
 *   // 输入：isLocalMode(config) === true, route = 'start_planner'
 *   // 行为：不创建 Issue，不写评论，只追加模型回复，goto: 'start-planner'。
 *   ```
 * - 场景 D：no_op
 *   ```ts
 *   // 输入：route = 'no_op'
 *   // 行为：只追加模型回复，goto: END。
 *   ```
 */
export async function classifyMessage(
  state: ManagerGraphState,
  config: GraphConfig,
): Promise<Command> {
  const userMessage = state.messages.findLast(isHumanMessage);
  if (!userMessage) {
    throw new Error("No human message found.");
  }

  let plannerThread: Thread<PlannerGraphState> | undefined;
  let programmerThread: Thread<GraphState> | undefined;
  let langGraphClient: Client | undefined;

  if (!isLocalMode(config)) {
    // Only create LangGraph client if not in local mode
    langGraphClient = createLangGraphClient({
      defaultHeaders: getDefaultHeaders(config),
    });

    plannerThread = state.plannerSession?.threadId
      ? await langGraphClient.threads.get(state.plannerSession.threadId)
      : undefined;
    const plannerThreadValues = plannerThread?.values;
    programmerThread = plannerThreadValues?.programmerSession?.threadId
      ? await langGraphClient.threads.get(
          plannerThreadValues.programmerSession.threadId,
        )
      : undefined;
  }

  const programmerStatus = programmerThread?.status ?? "not_started";
  const plannerStatus = plannerThread?.status ?? "not_started";

  // If the githubIssueId is defined, fetch the most recent task plan (if exists). Otherwise fallback to state task plan
  const issuePlans = state.githubIssueId
    ? await getPlansFromIssue(state, config)
    : null;
  const taskPlan = issuePlans?.taskPlan ?? state.taskPlan;

  const { prompt, schema } = createClassificationPromptAndToolSchema({
    programmerStatus,
    plannerStatus,
    messages: state.messages,
    taskPlan,
    proposedPlan: issuePlans?.proposedPlan ?? undefined,
    requestSource: userMessage.additional_kwargs?.requestSource as
      | RequestSource
      | undefined,
  });
  const respondAndRouteTool = {
    name: "respond_and_route",
    description: "Respond to the user's message and determine how to route it.",
    schema,
  };
  const model = await loadModel(config, LLMTask.ROUTER);
  const modelSupportsParallelToolCallsParam = supportsParallelToolCallsParam(
    config,
    LLMTask.ROUTER,
  );
  const modelWithTools = model.bindTools([respondAndRouteTool], {
    tool_choice: respondAndRouteTool.name,
    ...(modelSupportsParallelToolCallsParam
      ? {
          parallel_tool_calls: false,
        }
      : {}),
  });

  const response = await modelWithTools.invoke([
    {
      role: "system",
      content: prompt,
    },
    {
      role: "user",
      content: extractContentWithoutDetailsFromIssueBody(
        getMessageContentString(userMessage.content),
      ),
    },
  ]);

  const toolCall = response.tool_calls?.[0];
  if (!toolCall) {
    throw new Error("No tool call found.");
  }
  const toolCallArgs = toolCall.args as z.infer<
    typeof BASE_CLASSIFICATION_SCHEMA
  >;

  if (toolCallArgs.route === "no_op") {
    // If it's a no_op, just add the message to the state and return.
    const commandUpdate: ManagerGraphUpdate = {
      messages: [response],
    };
    return new Command({
      update: commandUpdate,
      goto: END,
    });
  }

  if ((toolCallArgs.route as string) === "create_new_issue") {
    // Route to node which kicks off new manager run, passing in the full conversation history.
    const commandUpdate: ManagerGraphUpdate = {
      messages: [response],
    };
    return new Command({
      update: commandUpdate,
      goto: "create-new-session",
    });
  }

  if (isLocalMode(config)) {
    // In local mode, just route to planner without GitHub issue creation
    const newMessages: BaseMessage[] = [response];
    const commandUpdate: ManagerGraphUpdate = {
      messages: newMessages,
    };

    if (
      toolCallArgs.route === "start_planner" ||
      toolCallArgs.route === "start_planner_for_followup"
    ) {
      return new Command({
        update: commandUpdate,
        goto: "start-planner",
      });
    }

    throw new Error(
      `Unsupported route for local mode received: ${toolCallArgs.route}`,
    );
  }

  const { githubAccessToken } = getGitHubTokensFromConfig(config);
  let githubIssueId = state.githubIssueId;

  const newMessages: BaseMessage[] = [response];

  // If it's not a no_op, ensure there is a GitHub issue with the user's request.
  if (!githubIssueId) {
    const { title } = await createIssueFieldsFromMessages(
      state.messages,
      config.configurable,
    );
    const { content: body } = extractIssueTitleAndContentFromMessage(
      getMessageContentString(userMessage.content),
    );

    const newIssue = await createIssue({
      owner: state.targetRepository.owner,
      repo: state.targetRepository.repo,
      title,
      body: formatContentForIssueBody(body),
      githubAccessToken,
    });
    if (!newIssue) {
      throw new Error("Failed to create issue.");
    }
    githubIssueId = newIssue.number;
    // Ensure we remove the old message, and replace it with an exact copy,
    // but with the issue ID & isOriginalIssue set in additional_kwargs.
    newMessages.push(
      ...[
        new RemoveMessage({
          id: userMessage.id ?? "",
        }),
        new HumanMessage({
          ...userMessage,
          additional_kwargs: {
            githubIssueId: githubIssueId,
            isOriginalIssue: true,
          },
        }),
      ],
    );
  } else if (
    githubIssueId &&
    state.messages.filter(isHumanMessage).length > 1
  ) {
    // If there already is a GitHub issue ID in state, and multiple human messages, add any
    // human messages to the issue which weren't already added.
    const messagesNotInIssue = state.messages
      .filter(isHumanMessage)
      .filter((message) => {
        // If the message doesn't contain `githubIssueId` in additional kwargs, it hasn't been added to the issue.
        return !message.additional_kwargs?.githubIssueId;
      });

    const createCommentsPromise = messagesNotInIssue.map(async (message) => {
      const createdIssue = await createIssueComment({
        owner: state.targetRepository.owner,
        repo: state.targetRepository.repo,
        issueNumber: githubIssueId,
        body: getMessageContentString(message.content),
        githubToken: githubAccessToken,
      });
      if (!createdIssue?.id) {
        throw new Error("Failed to create issue comment");
      }
      newMessages.push(
        ...[
          new RemoveMessage({
            id: message.id ?? "",
          }),
          new HumanMessage({
            ...message,
            additional_kwargs: {
              githubIssueId,
              githubIssueCommentId: createdIssue.id,
              ...((toolCallArgs.route as string) ===
              "start_planner_for_followup"
                ? {
                    isFollowup: true,
                  }
                : {}),
            },
          }),
        ],
      );
    });

    await Promise.all(createCommentsPromise);

    let newPlannerId: string | undefined;
    let goto = END;

    if (plannerStatus === "interrupted") {
      if (!state.plannerSession?.threadId) {
        throw new Error("No planner session found. Unable to resume planner.");
      }
      // We need to resume the planner session via a 'response' so that it can re-plan
      const plannerResume: HumanResponse = {
        type: "response",
        args: "resume planner",
      };
      logger.info("Resuming planner session");
      if (!langGraphClient) {
        throw new Error("LangGraph client not initialized");
      }
      const newPlannerRun = await langGraphClient.runs.create(
        state.plannerSession?.threadId,
        PLANNER_GRAPH_ID,
        {
          command: {
            resume: plannerResume,
          },
          streamMode: OPEN_SWE_STREAM_MODE as StreamMode[],
        },
      );
      newPlannerId = newPlannerRun.run_id;
      logger.info("Planner session resumed", {
        runId: newPlannerRun.run_id,
        threadId: state.plannerSession.threadId,
      });
    }

    if (toolCallArgs.route === "start_planner_for_followup") {
      goto = "start-planner";
    }

    // After creating the new comment, we can add the message to state and end.
    const commandUpdate: ManagerGraphUpdate = {
      messages: newMessages,
      ...(newPlannerId && state.plannerSession?.threadId
        ? {
            plannerSession: {
              threadId: state.plannerSession.threadId,
              runId: newPlannerId,
            },
          }
        : {}),
    };
    return new Command({
      update: commandUpdate,
      goto,
    });
  }

  // Issue has been created, and any missing human messages have been added to it.

  const commandUpdate: ManagerGraphUpdate = {
    messages: newMessages,
    ...(githubIssueId ? { githubIssueId } : {}),
  };

  if (
    (toolCallArgs.route as any) === "update_programmer" ||
    (toolCallArgs.route as any) === "update_planner" ||
    (toolCallArgs.route as any) === "resume_and_update_planner"
  ) {
    // If the route is one of the above, we don't need to do anything since the issue now contains
    // the new messages, and the coding agent will handle pulling them in. This should never be
    // reachable since we should return early after adding the Github comment, but include anyways...
    return new Command({
      update: commandUpdate,
      goto: END,
    });
  }

  if (
    toolCallArgs.route === "start_planner" ||
    toolCallArgs.route === "start_planner_for_followup"
  ) {
    // Always kickoff a new start planner node. This will enqueue new runs on the planner graph.
    return new Command({
      update: commandUpdate,
      goto: "start-planner",
    });
  }

  throw new Error(`Invalid route: ${toolCallArgs.route}`);
}
