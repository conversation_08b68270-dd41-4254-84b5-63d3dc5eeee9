import { v4 as uuidv4 } from "uuid";
import { GraphConfig } from "@open-swe/shared/open-swe/types";
import {
  ManagerGraphState,
  ManagerGraphUpdate,
} from "@open-swe/shared/open-swe/manager/types";
import { getGitHubTokensFromConfig } from "../../../utils/github-tokens.js";
import { HumanMessage, isHumanMessage } from "@langchain/core/messages";
import { getIssue } from "../../../utils/github/api.js";
import { extractTasksFromIssueContent } from "../../../utils/github/issue-task.js";
import { getMessageContentFromIssue } from "../../../utils/github/issue-messages.js";
import { isLocalMode } from "@open-swe/shared/open-swe/local-mode";

/**
 * 初始化管理器状态中的 GitHub Issue 关联消息与任务计划（TaskPlan）。
 *
 * 逻辑流转：
 * 1) 本地模式（Local Mode）
 *    - 直接返回空更新 `{}`。本地模式下不会访问 GitHub，通常 HumanMessage 已由 CLI 输入注入。
 *
 * 2) 状态中已存在至少一条 HumanMessage
 *    - 不创建新的消息。
 *    - 若存在 `githubIssueId`，则从对应 Issue 读取最新正文，尝试解析 `<open-swe-do-not-edit-task-plan>` 中的 TaskPlan，
 *      用以覆盖当前 `state.taskPlan`。随后返回 `{ taskPlan }`。
 *    - 若不存在 `githubIssueId`，则不访问 GitHub，直接返回 `{ taskPlan: state.taskPlan }`。
 *
 * 3) 状态中不存在任何消息
 *    - 视为首次初始化：要求 `state.githubIssueId` 与 `state.targetRepository` 必填，否则抛错。
 *    - 访问 GitHub 获取 Issue：
 *      - 若 Issue 正文中包含 TaskPlan 标签，解析并设置到 `taskPlan`；
 *      - 用该 Issue 生成一条 `HumanMessage`（标记 `isOriginalIssue: true`），作为对话的起始消息；
 *      - 返回 `{ messages: [newMessage], taskPlan }`。
 *
 * 错误处理：
 * - 在需要访问 GitHub 且缺少必要字段（`githubIssueId` 或 `targetRepository`）时抛错；
 * - 若远端 Issue 不存在也会抛错。
 *
 * 示例：
 * - 场景 A：Local Mode
 *   ```ts
 *   const update = await initializeGithubIssue({ messages: [new HumanMessage("cli input")] } as any, configInLocalMode);
 *   // => {}
 *   ```
 *
 * - 场景 B：已有用户消息（仅刷新 TaskPlan）
 *   ```ts
 *   const state = {
 *     messages: [new HumanMessage("user says ...")],
 *     githubIssueId: 123,
 *     targetRepository: { owner: "acme", repo: "demo" },
 *     taskPlan: null,
 *   } as ManagerGraphState;
 *   const update = await initializeGithubIssue(state, config);
 *   // => { taskPlan } // 若 Issue 中存在 <open-swe-do-not-edit-task-plan> 则解析覆盖
 *   ```
 *
 * - 场景 C：首次初始化（无消息 → 从 Issue 注入原始消息）
 *   ```ts
 *   const state = {
 *     messages: [],
 *     githubIssueId: 123,
 *     targetRepository: { owner: "acme", repo: "demo" },
 *   } as ManagerGraphState;
 *   const update = await initializeGithubIssue(state, config);
 *   // => { messages: [HumanMessage(标记 isOriginalIssue)], taskPlan }
 *   ```
 */
export async function initializeGithubIssue(
  state: ManagerGraphState,
  config: GraphConfig,
): Promise<ManagerGraphUpdate> {
  if (isLocalMode(config)) {
    // In local mode, we don't need GitHub issues
    // The human message should already be in the state from the CLI input
    return {};
  }
  const { githubInstallationToken } = getGitHubTokensFromConfig(config);
  let taskPlan = state.taskPlan;

  if (state.messages.length && state.messages.some(isHumanMessage)) {
    // If there are messages, & at least one is a human message, only attempt to read the updated plan from the issue.
    if (state.githubIssueId) {
      const issue = await getIssue({
        owner: state.targetRepository.owner,
        repo: state.targetRepository.repo,
        issueNumber: state.githubIssueId,
        githubInstallationToken,
      });
      if (!issue) {
        throw new Error("Issue not found");
      }
      if (issue.body) {
        // 取 <open-swe-do-not-edit-task-plan> 标签内的内容
        const extractedTaskPlan = extractTasksFromIssueContent(issue.body);
        if (extractedTaskPlan) {
          taskPlan = extractedTaskPlan;
        }
      }
    }

    return {
      taskPlan,
    };
  }

  // If there are no messages, ensure there's a GitHub issue to fetch the message from.
  if (!state.githubIssueId) {
    throw new Error("GitHub issue ID not provided");
  }
  if (!state.targetRepository) {
    throw new Error("Target repository not provided");
  }

  const issue = await getIssue({
    owner: state.targetRepository.owner,
    repo: state.targetRepository.repo,
    issueNumber: state.githubIssueId,
    githubInstallationToken,
  });
  if (!issue) {
    throw new Error("Issue not found");
  }
  if (issue.body) {
    // 从issue body中提取 task plan，第一次进来时，issue body 中没有 task plan，所以 taskPlan 为空
    const extractedTaskPlan = extractTasksFromIssueContent(issue.body);
    if (extractedTaskPlan) {
      taskPlan = extractedTaskPlan;
    }
  }

  const newMessage = new HumanMessage({
    id: uuidv4(),
    content: getMessageContentFromIssue(issue),
    additional_kwargs: {
      githubIssueId: state.githubIssueId,
      isOriginalIssue: true,
    },
  });

  return {
    messages: [newMessage],
    taskPlan,
  };
}
