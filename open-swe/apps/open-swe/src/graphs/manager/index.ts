import { END, START, StateGraph } from "@langchain/langgraph";
import { GraphConfiguration } from "@open-swe/shared/open-swe/types";
import { ManagerGraphStateObj } from "@open-swe/shared/open-swe/manager/types";
import {
  initializeGithubIssue,
  classifyMessage,
  startPlanner,
  createNewSession,
} from "./nodes/index.js";

/**
 * 两个参数的含义：
 * 1. configSchema 配置了 ManagerGraphState 的类型和初始值(包括 githubIssueId, githubPullRequestId, targetRepository, taskPlan, programmerSession, plannerSession, branchName, autoAcceptPlan)
 * 2. GraphConfiguration 配置了 graph 的配置(包括 thread_id, run_id, maxContextActions, maxReviewActions, plannerModelName, programmerModelName, temperature)
 */
const workflow = new StateGraph(ManagerGraphStateObj, GraphConfiguration)
  .addNode("initialize-github-issue", initializeGithubIssue)
  .addNode("classify-message", classifyMessage, {
    ends: [END, "start-planner", "create-new-session"],
  })
  .addNode("create-new-session", createNewSession)
  .addNode("start-planner", startPlanner)
  .addEdge(START, "initialize-github-issue")
  .addEdge("initialize-github-issue", "classify-message")
  .addEdge("create-new-session", END)
  .addEdge("start-planner", END);

export const graph = workflow.compile();
graph.name = "Open SWE - Manager";
