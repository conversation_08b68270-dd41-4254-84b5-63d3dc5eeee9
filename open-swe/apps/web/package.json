{"name": "@open-swe/web", "readme": "https://github.com/langchain-ai/open-swe/blob/main/apps/web/README.md", "homepage": "https://github.com/langchain-ai/open-swe/blob/main/README.md", "repository": {"type": "git", "url": "git+https://github.com/langchain-ai/open-swe.git"}, "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "rm -rf .turbo .next || true && rm next-env.d.ts || true"}, "dependencies": {"@langchain/core": "^0.3.65", "@langchain/langgraph": "^0.3.8", "@langchain/langgraph-sdk": "^0.0.95", "@octokit/app": "^16.0.1", "@open-swe/shared": "*", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "esbuild": "^0.25.0", "esbuild-plugin-tailwindcss": "^2.0.1", "framer-motion": "^12.4.9", "jsonwebtoken": "^9.0.2", "katex": "^0.16.21", "langgraph-nextjs-api-passthrough": "^0.1.4", "lodash": "^4.17.21", "lucide-react": "^0.532.0", "next-themes": "^0.4.4", "nuqs": "^2.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.0.1", "react-syntax-highlighter": "^15.5.0", "recharts": "^2.15.1", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.1", "swr": "^2.3.4", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "use-stick-to-bottom": "^1.0.46", "uuid": "^11.1.0", "zod": "^3.25.32", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.19.0", "@octokit/types": "^14.1.0", "@tailwindcss/postcss": "^4.0.13", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.16", "@types/node": "^22.13.5", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "dotenv": "^16.4.7", "eslint": "^9.19.0", "eslint-config-next": "15.2.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "next": "^15.2.3", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "shadcn": "^2.6.1", "tailwind-scrollbar": "^4.0.1", "tailwindcss": "^4.0.13", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0"}, "overrides": {"react-is": "^19.0.0-rc-69d4b800-20241021"}, "packageManager": "yarn@3.5.1"}