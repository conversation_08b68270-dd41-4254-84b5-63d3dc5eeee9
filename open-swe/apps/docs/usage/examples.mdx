---
title: Examples
description: A collection of example tasks for Open SWE
---

If you want to try out Open SWE, but aren't sure where to start, here are a few example tasks you can try out.

<Steps>
  <Step title="Clone the TypeScript template">
    The examples below are all for TypeScript tasks. For these, you should clone an empty TypeScript template repository:

    [bracesproul/typescript-template](https://github.com/bracesproul/typescript-template)

    Visit the GitHub UI, and click `Use this template` to create a new repository based on this template.

  </Step>

  <Step title="Give Open SWE access to your repository">
    If you did not give Open SWE to all of your repositories when setting it up the first time, you'll need to give Open SWE access to this repository.

    1. Visit the settings page on Open SWE: [swe.langchain.com/settings](https://swe.langchain.com/settings)
    2. To the right of your user inside the `Current User` section, click the `+` button. This will redirect you to GitHub where you can authorize Open SWE to access your repositories.
    3. Select the new TypeScript template you just created.

    Once done, it will redirect you back to Open SWE. Once here, find the new repository under the repo dropdown above the chat input.

  </Step>

  <Step title="Submit one of the example tasks">
    After cloning the template and giving Open SWE access to it, you can submit one of the example tasks!
  </Step>
</Steps>

Below are a series of examples you can try out:

<Accordion title="RESTful 'Books' micro-service">
  ```txt
  Create an Express-based API exposing CRUD endpoints for `/books`.
  Include request validation with `zod`, proper HTTP status codes, and an
  in-memory repository layer that can later be swapped for a database. Add Jest
  tests covering happy-path and a 'missing ISBN' error case.
  ```
</Accordion>

<Accordion title="WebSocket live counter">
  ```txt
  Add a small WebSocket server (using `ws`) that tracks how many clients
  are currently connected and broadcasts the updated count every 5 seconds.
  Expose a health-check HTTP route returning the same value for monitoring
  tools. Provide a minimal HTML demo page that shows the live number.
  ```
</Accordion>

<Accordion title="GitHub issue sync CLI">
  ```txt
  Implement a CLI (`src/sync-issues.ts`) that reads a `config.json` with
  one or more GitHub repos, fetches their open issues via the GitHub REST API,
  and writes a local `issues.{owner}.{repo}.csv`. Use `yargs` for parsing and
  include a `--since YYYY-MM-DD` flag to filter by creation date.
  ```
</Accordion>

<Accordion title="JWT + refresh-token auth layer">
  ```txt
  Build a middleware that issues short-lived access tokens and long-lived
  refresh tokens. Store refresh tokens in a signed, HttpOnly cookie and expose
  `/auth/refresh` to rotate them. Protect a sample route (`/profile`) and supply
  Postman collections for login and refresh flows.
  ```
</Accordion>

<Accordion title="Pluggable caching service">
  ```txt
  Design a generic cache interface with `get`, `set`, and `invalidate`
  methods, then provide two adapters: an in-memory `Map` implementation and a
  Redis adapter (mock Redis with `ioredis-mock` for tests). Demonstrate
  hot-swapping the adapter via an environment variable without code changes.
  ```
</Accordion>

<Accordion title="GraphQL wrapper over REST API">
  ```txt
  Stand up an Apollo Server that federates data from the public
  JSONPlaceholder `/users` and `/posts` endpoints. Expose a `user(id)` query
  returning the user plus their posts in a single round-trip. Add schema-driven
  TypeScript types (`codegen.yml`) and example queries in `README.md`.
  ```
</Accordion>

<Accordion title="Image upload to S3 with signed URLs">
  ```txt
  Create an endpoint that returns a time-limited pre-signed PUT URL for
  an S3 bucket (use `@aws-sdk/client-s3`). Include a small React demo (Vite)
  that lets a user pick an image and upload it directly. Validate MIME type on
  the server before signing.
  ```
</Accordion>

<Accordion title="Locale-aware date utility library">
  ```txt
  Publish an internal `/src/date` module that formats, parses, and
  time-zone-converts dates using `luxon`. Support at least 'en-US', 'de-DE', and
  'ja-JP'. Provide type-safe wrappers, exhaustive unit tests, and a benchmark
  script comparing it to native `Intl.DateTimeFormat`.
  ```
</Accordion>
