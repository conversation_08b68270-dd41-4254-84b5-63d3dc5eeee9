---
title: "Introduction"
description: "How to set up Open SWE for development"
---

# Development Setup Overview

Welcome to the Open SWE development setup guide. This section will walk you through everything you need to know to get Open SWE running locally for development.

## Setup Sections

The setup process is organized into focused sections to help you get up and running efficiently:

<CardGroup cols={2}>
  <Card
    title="Development Setup"
    icon="code"
    href="/labs/swe/setup/development"
  >
    Complete guide to cloning the repository, installing dependencies,
    configuring environment variables, and starting the development servers.
  </Card>
  <Card
    title="Authentication"
    icon="shield-check"
    href="/labs/swe/setup/authentication"
  >
    Understanding the authentication flow, GitHub App configuration, and
    security mechanisms used throughout Open SWE.
  </Card>
</CardGroup>

## Technology Stack

Open SWE is built with modern technologies designed for scalability and developer experience:

### Core Technologies

- **TypeScript** - Strict type safety across the entire codebase
- **Yarn** - Package manager with workspace support (v3.5.1)
- **Turbo** - Monorepo build orchestration and task running

### Agent Infrastructure

- **LangGraph** - Multi-agent orchestration framework with three specialized graphs:
  - Manager graph for user interaction orchestration
  - Planner graph for execution plan creation
  - Programmer graph for code change execution
- **Daytona** - Sandboxed development environments for safe code execution

### Web Application

- **Next.js** - React framework with App Router
- **Shadcn UI** - Component library built on Radix UI primitives
- **Tailwind CSS** - Utility-first CSS framework

### Documentation

- **Mintlify** - Documentation platform with MDX support

## Monorepo Structure

<Note>
  Open SWE uses a Yarn workspace monorepo with three main applications and a
  shared package for common utilities and types.
</Note>

- **`apps/open-swe`** - LangGraph agent application
- **`apps/web`** - Next.js web interface
- **`apps/docs`** - Mintlify documentation site
- **`packages/shared`** - Shared utilities, types, and constants

The monorepo is orchestrated by Turbo, which handles build dependencies and parallel task execution across packages.
