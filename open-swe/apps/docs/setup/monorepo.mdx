---
title: "Monorepo"
description: "How the Open SWE monorepo is configured"
---

Open SWE is organized as a Yarn workspace monorepo with Turbo build orchestration, designed to efficiently manage multiple applications and shared code. This structure enables code reuse, consistent tooling, and streamlined development workflows.

## Repository Structure

The monorepo is organized into two main directories:

### Applications (`apps/`)

**`apps/open-swe`** - LangGraph Agent Application

- Contains the core LangGraph agent implementation with TypeScript
- Includes three specialized graphs: manager, planner, and programmer
- <PERSON>les GitHub webhook integration and LLM interactions

**`apps/web`** - Next.js Web Interface

- React frontend with Next.js framework
- Uses Shadcn UI components (Radix UI) with Tailwind CSS
- Provides the user interface for interacting with the LangGraph agent
- Includes authentication and proxy routes for secure communication

**`apps/docs`** - Mintlify Documentation

- Contains this documentation site built with Mintlify
- Provides comprehensive setup and usage guides
- Includes API documentation and development resources

### Packages (`packages/`)

**`packages/shared`** - Common Utilities Package

- Centralized location for shared types, constants, and utilities
- Used by both the agent and web applications
- Exports modules via `@open-swe/shared` namespace
- Contains crypto utilities, GraphState types, and Open SWE specific modules

<Note>
  The shared package must be built before other packages can import from it. Any
  code used by both the agent and web app should be placed here to avoid
  duplication.
</Note>

## Turbo Orchestration

The monorepo uses [Turbo](https://turbo.build/) for efficient build orchestration and task management:

### Task Dependencies

```json
{
  "build": {
    "dependsOn": ["^build"],
    "outputs": ["dist/**", ".next/**", "!.next/cache/**"]
  },
  "lint": {
    "dependsOn": ["^lint"]
  }
}
```

The `^build` dependency ensures that shared packages are built before dependent packages, maintaining proper build order across the monorepo.

### Available Scripts

Run these commands from the repository root:

- `yarn build` - Build all packages in dependency order
- `yarn lint` - Run linting across all packages
- `yarn format` - Format code using Prettier

## Dependency Management Best Practices

<Steps>
  <Step title="Install dependencies in specific packages">
    Always install dependencies in the specific package where they're used, never in the root `package.json` unless adding a resolution.
    
    ```bash
    # Correct - install in specific package
    cd apps/web
    yarn add some-package
    
    # Incorrect - don't install in root
    yarn add some-package
    ```
  </Step>
  
  <Step title="Use resolutions for shared dependencies">
    When multiple packages need the same dependency, add a resolution to the root `package.json` to ensure version consistency.
    
    ```json
    {
      "resolutions": {
        "@langchain/langgraph-sdk": "^0.0.95",
        "@langchain/core": "^0.3.58"
      }
    }
    ```
  </Step>
  
  <Step title="Build shared packages after changes">
    Run `yarn build` from the root when making changes to `packages/shared` to make them available to other packages.
    
    ```bash
    # After modifying packages/shared
    yarn build
    ```
  </Step>
</Steps>

## Postinstall Hook Requirements

The `apps/open-swe` package includes a critical `postinstall` hook:

```json
{
  "scripts": {
    "postinstall": "turbo build"
  }
}
```

<Note>
  This postinstall hook is **required** for LangGraph Platform deployment. Since
  Open SWE is a monorepo and the agent requires access to built files from the
  shared package, we must run the build process before starting the LangGraph
  server.
</Note>

### Why This Matters

1. **Deployment Compatibility**: The LangGraph Platform needs all dependencies built and available
2. **Shared Package Access**: The agent imports utilities from `@open-swe/shared` which must be compiled
3. **Build Order**: Ensures the shared package is built before the agent attempts to use it

<Tip>
  If you encounter import errors related to the shared package during
  development, run `yarn build` from the root to ensure all packages are
  properly built and linked.
</Tip>

## Workspace Configuration

The monorepo uses Yarn 3.5.1 with the following configuration:

- **Node Linker**: `node-modules` for compatibility
- **Workspaces**: Automatically discovers packages in `apps/*` and `packages/*`
- **Package Manager**: Enforced via `packageManager` field in `package.json`
