---
title: "Authentication"
description: "How authentication works in Open SWE"
---

Open SWE implements a comprehensive authentication system that secures both client-side interactions and server-side operations. The authentication flow involves GitHub OAuth for user authentication, encrypted token handling, and multi-layered security for LangGraph server requests.

## GitHub OAuth Authentication

Open SWE uses GitHub OAuth for client-side authentication, providing secure access to user accounts and repository permissions.

### Authentication Flow

- **Unauthenticated users** are automatically redirected to GitHub OAuth login
- **Authenticated users** are redirected directly to the chat interface
- **Settings management** is available at `/settings` for updating GitHub authentication

<Note>
  GitHub OAuth provides the foundation for all user interactions, enabling Open
  SWE to access repositories and perform actions on behalf of authenticated
  users.
</Note>

## LangGraph Server Authentication

All requests to the LangGraph server are authenticated through a sophisticated proxy system that ensures secure communication between the web interface and the agent backend.

### Proxy Route Architecture

The Next.js application includes a proxy route (`apps/web/src/app/api/[..._path]/route.ts`) that acts as an intermediary for all LangGraph server requests. This proxy uses the [`langgraph-nextjs-api-passthrough`](https://www.npmjs.com/package/langgraph-nextjs-api-passthrough) package to handle request forwarding with enhanced security.

<Tip>
  The proxy route ensures that sensitive authentication tokens never reach the
  client directly, maintaining security while enabling seamless communication
  with the LangGraph server.
</Tip>

## Simple API Key (Bearer) Authentication

For local development and scripted access, the LangGraph server also supports a simple bearer token scheme. When a request includes an `Authorization: Bearer <token>` header, this path is used and the rest of the auth flow is skipped.

### Setup (development)

- **Generate a token** (32+ bytes, URL-safe):
  - OpenSSL: `openssl rand -hex 32`
- **Add to your `.env` for the LangGraph server**:

```bash
API_BEARER_TOKEN=<your-generated-token>
```

- For multiple/rotation: use comma-separated tokens

```bash
API_BEARER_TOKENS=<token1>,<token2>,<token3>
```

Restart the LangGraph server after updating environment variables.

### Usage

```typescript
import { Client } from "@langchain/langgraph-sdk";

const client = new Client({
  apiUrl: process.env.LANGGRAPH_API_URL,
  defaultHeaders: {
    authorization: `Bearer ${process.env.API_BEARER_TOKEN}`,
  },
});
```

### Notes

- **Precedence**: If the `Authorization` header is present, bearer auth is used; otherwise the existing GitHub-based flow applies.
- **Rotation**: Add a new token to `API_BEARER_TOKENS`, deploy/restart, migrate clients, then remove old tokens and redeploy.
- **Scope**: All bearer tokens currently map to the same internal identity and permissions. If you need per-token identities/quotas, reach out to adjust the configuration.

### Header Injection System

The proxy route automatically injects the following encrypted headers into each request:

#### Authentication Headers

- **`x-github-access-token`** - User's GitHub access token for user-specific actions (creating issues, comments)
- **`x-github-installation-token`** - GitHub App installation token for app-level actions (commits, pull requests)
- **`x-github-installation-name`** - Installation name (username or organization name)

<Note>
  All headers are prefixed with `x-` to ensure they're included in LangGraph run
  configurations, making them accessible during execution while maintaining
  security through encryption.
</Note>

### Token Encryption

Open SWE implements AES-256-GCM encryption for all secrets passed to the LangGraph server to prevent exposure in:

- LangSmith trace metadata
- Run configurations
- Potential unauthorized access scenarios

The encryption process uses the `SECRETS_ENCRYPTION_KEY` environment variable and includes:

- **Initialization Vector (IV)** for unique encryption per token
- **Authentication Tag** for data integrity verification
- **Base64 encoding** for safe transport

<Tip>
  The same encryption key must be configured in both the web application and
  LangGraph agent for proper token decryption.
</Tip>

## Authentication Middleware

The LangGraph server implements comprehensive authentication middleware (`apps/open-swe/src/security/auth.ts`) that validates all incoming requests.

### Webhook Authentication

The middleware first checks for GitHub webhook requests by detecting the `X-Hub-Signature-256` header:

1. **Signature verification** using the configured webhook secret
2. **Automatic authorization** for valid webhook signatures
3. **Separate user verification** in subsequent run creation requests

<Note>
  Webhook authentication is handled separately from user authentication to
  enable automated GitHub issue processing while maintaining security.
</Note>

### Standard Request Authentication

For non-webhook requests, the middleware validates:

#### Required Headers

- **Installation name** (`x-github-installation-name`)
- **Installation token** (`x-github-installation-token`)

Missing either header results in a 401 Unauthorized error.

#### User Verification Process

The middleware supports two authentication paths:

**Web Application Requests:**

- Uses encrypted GitHub access token (`x-github-access-token`)
- Verifies user identity through GitHub API
- Extracts user ID and login from token

**Webhook-Generated Requests:**

- Uses explicit user headers (`x-github-user-id`, `x-github-user-login`)
- Validates user ID and login against installation token
- Ensures webhook-created runs are properly attributed

### Identity and Permissions

Upon successful authentication, the middleware returns an identity object containing:

- **User ID** for resource ownership verification
- **Display name** (GitHub login)
- **Installation name** for repository context
- **Comprehensive permissions** for LangGraph operations

<Tip>
  The user ID serves as the primary identifier for resource access control,
  ensuring users can only access their own threads, runs, and assistants.
</Tip>

## Resource Access Control

Open SWE implements fine-grained access control for all LangGraph resources:

### Metadata-Based Ownership

- **Create operations** automatically add user ID to resource metadata
- **Read/Update/Delete operations** verify user ID matches resource owner
- **Search operations** filter results by user ownership

## Token Access During Execution

During LangGraph run execution, encrypted tokens are accessible through the run's configurable field:

1. **Token extraction** from run configuration
2. **Decryption** using the shared encryption key
3. **Action execution** (creating issues, making commits, etc.)

<Note>
  This design ensures tokens remain encrypted in storage and traces while being
  available for necessary GitHub operations during run execution.
</Note>

<Tip>
  Always ensure the `SECRETS_ENCRYPTION_KEY` environment variable is identical
  between your web application and LangGraph agent deployments.
</Tip>
