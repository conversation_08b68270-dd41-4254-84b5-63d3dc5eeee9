'use client';

import { usePathname } from 'next/navigation';
import { ReactNode } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Sparkles, MessageCircle, Code2 } from 'lucide-react';

import { cn } from '@/utils/cn';

export const ActiveLink = (props: { href: string; children: ReactNode; icon?: ReactNode }) => {
  const pathname = usePathname();
  const isActive = pathname === props.href;

  return (
    <Button
      asChild
      variant="ghost"
      className={cn(
        'px-4 py-2.5 rounded-full whitespace-nowrap flex items-center gap-2 text-sm font-medium transition-all duration-300 hover:scale-105',
        isActive
          ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm border border-white/30'
          : 'text-white/80 hover:text-white hover:bg-white/10'
      )}
    >
      <Link href={props.href} className="flex items-center gap-2">
        {props.icon}
        {props.children}
      </Link>
    </Button>
  );
};

export function Navbar({ title }: { title: string }) {
  return (
    <div className="relative overflow-hidden">
      {/* 背景渐变 */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600"></div>
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/80 via-purple-500/80 to-pink-500/80 animate-pulse"></div>

      {/* 装饰性元素 */}
      <div className="absolute top-0 left-1/4 w-32 h-32 bg-white/10 rounded-full blur-xl animate-float"></div>
      <div className="absolute bottom-0 right-1/3 w-24 h-24 bg-white/5 rounded-full blur-lg animate-float" style={{animationDelay: '1s'}}></div>

      <div className="relative flex items-center justify-between p-4 backdrop-blur-sm">
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-10 h-10 bg-white/20 rounded-full backdrop-blur-sm border border-white/30">
            <Sparkles className="w-5 h-5 text-white animate-pulse" />
          </div>
          <h1 className="text-xl font-bold text-white bg-gradient-to-r from-white to-white/80 bg-clip-text">
            {title}
          </h1>
        </div>

        <nav className="flex items-center gap-3">
          <ActiveLink href="/" icon={<MessageCircle className="w-4 h-4" />}>
            聊天
          </ActiveLink>
          <ActiveLink href="/ai-coding" icon={<Code2 className="w-4 h-4" />}>
            AI编程
          </ActiveLink>
        </nav>
      </div>
    </div>
  );
}
