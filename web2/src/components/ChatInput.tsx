import { type FormEvent, ReactNode } from 'react';
import { ArrowUpIcon, LoaderCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/utils/cn';

interface ChatInputProps {
  onSubmit: (e: FormEvent<HTMLFormElement>) => void;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  loading?: boolean;
  placeholder?: string;
  children?: ReactNode;
  className?: string;
  disabled?: boolean;
  buttonColor?: string;
  buttonHoverColor?: string;
}

export function ChatInput({
  onSubmit,
  value,
  onChange,
  loading = false,
  placeholder = "输入消息...",
  children,
  className,
  disabled = false,
  buttonColor = "bg-blue-600",
  buttonHoverColor = "hover:bg-blue-700"
}: ChatInputProps) {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSubmit(e as unknown as FormEvent<HTMLFormElement>);
    }
  };

  const isSubmitDisabled = loading || disabled || !value.trim();

  return (
    <form
      onSubmit={(e) => {
        e.stopPropagation();
        e.preventDefault();
        onSubmit(e);
      }}
      className={cn('flex w-full flex-col', className)}
    >
      <div className="bg-white rounded-3xl shadow-lg border border-gray-200 max-w-[800px] w-full mx-auto overflow-hidden">
        <div className="flex items-end p-4">
          <Textarea
            value={value}
            placeholder={placeholder}
            onChange={onChange}
            onKeyDown={handleKeyDown}
            className="border-none outline-none bg-transparent flex-grow resize-none text-base leading-6 min-h-[24px] max-h-[200px] placeholder:text-gray-500"
            rows={1}
            style={{
              minHeight: '24px',
              maxHeight: '200px',
              lineHeight: '1.5'
            }}
            disabled={loading}
          />

          <div className="flex items-center ml-3">
            {children}
            <Button
              className={cn(
                "rounded-full p-2 h-10 w-10 text-white transition-colors",
                buttonColor,
                buttonHoverColor,
                isSubmitDisabled && "opacity-50 cursor-not-allowed"
              )}
              type="submit"
              disabled={isSubmitDisabled}
            >
              {loading ? (
                <LoaderCircle className="animate-spin h-5 w-5" />
              ) : (
                <ArrowUpIcon className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </form>
  );
}
