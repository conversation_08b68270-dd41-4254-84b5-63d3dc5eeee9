import { StateGraph, MemorySaver, END, START } from "@langchain/langgraph";
import { AIMessage, BaseMessage } from "@langchain/core/messages";
import { v4 as uuidv4 } from "uuid";
import { graph as designToHtmlGraph } from "./nodes/designToHtml/index";
import { graph as combineHtmlGraph } from "./nodes/combineHtml/index";
import { graph as genProjectCodeGraph } from "./nodes/genProjectCode/index";
import { DesignItem, RefactoredDesignItem } from "./types";

interface DesignToCodeState {
  messages: BaseMessage[];
  error: string | undefined;

  // 输入参数（只读）
  input: DesignItem[];

  output: string; // 添加输出字段
  // 并行处理阶段
  htmlResults: RefactoredDesignItem[];

  // 合并阶段
  combinedHtml?: string;

  // 项目生成阶段
  projectCode?: string;
}

const mainCheckpointer = new MemorySaver();

// 状态对象定义
const DesignToCodeStateObj = {
  messages: {
    value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
    default: () => [],
  },
  // 图的输入，这里是设计稿内容
  input: {
    value: (x: DesignItem[], y: DesignItem[]) => y,
    default: () => [],
  },
  // 图的输出
  output: {
    value: (x: string, y: string) => y,
    default: () => "",
  },
  // html的中间结果
  htmlResults: {
    value: (x: RefactoredDesignItem[], y: RefactoredDesignItem[]) => y,
    default: () => [],
  },
  // html合并的中间结果
  combinedHtml: {
    value: (x: string | undefined, y: string | undefined) => y,
    default: () => undefined,
  },
  // 项目代码的结果
  projectCode: {
    value: (x: string | undefined, y: string | undefined) => y,
    default: () => undefined,
  },
  // 错误信息
  error: {
    value: (x: string | undefined, y: string | undefined) => y,
    default: () => undefined,
  },
};

/**
 * 条件路由函数 - 验证输入并记录日志
 */
function parseInput(state: DesignToCodeState): "parallel" | "errorHandler" {
  if (!state.input || !Array.isArray(state.input) || state.input.length === 0) {
    console.log("DesignToCode: 输入验证失败 - 没有有效的设计稿");
    return "errorHandler";
  }

  const designCount = state.input.length;
  console.log(`DesignToCode: 输入验证通过 - 解析到 ${designCount} 个设计稿`);

  return "parallel";
}

/**
 * 并行执行 designToHtml 节点
 */
async function parallelDesignToHtmlNode(state: DesignToCodeState) {
  console.log("DesignToCode: 开始并行执行 designToHtml...");

  if (state.input.length === 0) {
    return {
      error: "没有找到有效的设计稿",
      messages: [new AIMessage("没有找到有效的设计稿")],
    };
  }

  // 并行启动所有 designToHtml 处理
  const promises = state.input.map(async (page, index) => {
    console.log(`DesignToCode: 启动第 ${index + 1} 个 designToHtml 处理`);
    const subThreadId = uuidv4();
    const result = await designToHtmlGraph.invoke(
      {
        input: page,
      },
      { configurable: { thread_id: subThreadId } }
    );

    return result.output;
  });

  // 等待所有处理完成
  const htmlResults = await Promise.all(promises);

  console.log(
    `DesignToCode: 所有 designToHtml 处理完成，共 ${state.input.length} 个`
  );

  return {
    htmlResults,
    messages: [
      new AIMessage(`并行处理完成，共 ${state.input.length} 个设计稿`),
    ],
  };
}

/**
 * 合并 HTML 节点
 */
async function combineHtmlNode(state: DesignToCodeState) {
  console.log("DesignToCode: 开始合并 HTML...");

  const subThreadId = uuidv4();
  // 调用独立的 combineHtml graph
  const combineResult = await combineHtmlGraph.invoke(
    {
      input: state.htmlResults,
    },
    { configurable: { thread_id: subThreadId } }
  );

  console.log("DesignToCode: HTML 合并完成");

  return {
    combinedHtml: combineResult.output,
    messages: [new AIMessage("HTML 合并完成")],
  };
}

/**
 * 生成项目代码节点
 */
async function genProjectCodeNode(state: DesignToCodeState) {
  console.log("DesignToCode: 开始生成项目代码...");

  if (!state.combinedHtml) {
    return {
      error: "没有合并后的 HTML 内容",
      messages: [new AIMessage("没有合并后的 HTML 内容")],
    };
  }

  const subThreadId = uuidv4();
  // 调用独立的 genProjectCode graph
  const genResult = await genProjectCodeGraph.invoke(
    {
      input: state.combinedHtml,
    },
    { configurable: { thread_id: subThreadId } }
  );

  console.log("DesignToCode: 项目代码生成完成");

  return {
    projectCode: genResult.projectCode,
    output: genResult.output,
    messages: [new AIMessage("项目代码生成完成")],
  };
}

/**
 * 错误处理节点
 */
async function errorHandlerNode(state: DesignToCodeState) {
  const errorMessage = state.error || "未知错误";
  console.error("DesignToCode: 工作流错误:", errorMessage);

  // 为没有设计稿的情况提供友好的错误消息
  let userFriendlyMessage = "";
  if (errorMessage.includes("没有有效的设计稿")) {
    userFriendlyMessage = "抱歉，AI编程助手目前需要您上传设计稿文件才能工作。请上传HTML文件或图片文件，我将帮您分析并生成相应的代码。\n\n如果您有一般的编程问题，建议您使用主页的聊天功能。";
  } else {
    userFriendlyMessage = `处理过程中遇到问题：${errorMessage}`;
  }

  const responseMessage = new AIMessage(userFriendlyMessage);

  return {
    output: userFriendlyMessage,
    messages: [responseMessage],
  };
}

function isParallelSuccessful(
  state: DesignToCodeState
): "combine" | "errorHandler" {
  if (state.error) {
    return "errorHandler";
  }
  return "combine";
}

function isCombineSuccessful(
  state: DesignToCodeState
): "generate" | "errorHandler" {
  return "generate";
}

function isGenerateSuccessful(
  state: DesignToCodeState
): "end" | "errorHandler" {
  return "end";
}

// 构建完整的设计到代码工作流图
const workflow = new StateGraph<DesignToCodeState>({
  channels: DesignToCodeStateObj,
})
  .addNode("parallelDesignToHtml", parallelDesignToHtmlNode)
  .addNode("combineHtml", combineHtmlNode)
  .addNode("genProjectCode", genProjectCodeNode)
  .addNode("errorHandler", errorHandlerNode)
  .addConditionalEdges(START, parseInput, {
    parallel: "parallelDesignToHtml",
    errorHandler: "errorHandler",
  })
  .addConditionalEdges("parallelDesignToHtml", isParallelSuccessful, {
    combine: "combineHtml",
    errorHandler: "errorHandler",
  })
  .addConditionalEdges("combineHtml", isCombineSuccessful, {
    generate: "genProjectCode",
    errorHandler: "errorHandler",
  })
  .addConditionalEdges("genProjectCode", isGenerateSuccessful, {
    end: END,
    errorHandler: "errorHandler",
  })
  .addEdge("errorHandler", END);

// 编译工作流
export const graph = workflow.compile({ checkpointer: mainCheckpointer });
graph.name = "designToCode";

// 导出子图供其他模块使用
export { graph as designToHtmlGraph } from "./nodes/designToHtml/index";
