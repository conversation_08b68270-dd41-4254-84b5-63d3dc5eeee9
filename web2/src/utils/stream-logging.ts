/**
 * Enhanced stream processor for LangGraph events with better intermediate step handling
 * Supports both development logging and production streaming with user feedback
 */

interface StreamState {
  currentStep?: string;
  toolCalls: Map<string, any>;
  intermediateSteps: string[];
  isThinking: boolean;
}

interface StreamEvent {
  event: string;
  data: any;
  name?: string;
  tags?: string[];
}

export function logToolCallsInDevelopment(eventStream: ReadableStream) {
  const streamState: StreamState = {
    toolCalls: new Map(),
    intermediateSteps: [],
    isThinking: false
  };

  return eventStream.pipeThrough(
    new TransformStream({
      async transform(chunk, controller) {
        try {
          // 处理 LangGraph streamEvents 格式
          if (chunk && typeof chunk === 'object') {
            const event = chunk as StreamEvent;

            // 处理不同类型的事件
            await processLangGraphEvent(event, streamState, controller);
          }

          // 始终传递原始chunk
          controller.enqueue(chunk);
        } catch (error) {
          console.error('Error processing stream chunk:', error);
          controller.enqueue(chunk);
        }
      },
      flush() {
        if (process.env.NODE_ENV === 'development') {
          console.log('Final stream state:', {
            toolCalls: Object.fromEntries(streamState.toolCalls),
            intermediateSteps: streamState.intermediateSteps
          });
        }
        streamState.toolCalls.clear();
        streamState.intermediateSteps = [];
      },
    }),
  );
}

async function processLangGraphEvent(
  event: StreamEvent,
  state: StreamState,
  _controller?: TransformStreamDefaultController
) {
  const { event: eventType, data, name } = event;

  switch (eventType) {
    case 'on_chat_model_start':
      state.isThinking = true;
      state.currentStep = 'thinking';
      logStep('🧠 AI开始思考...', state);
      break;

    case 'on_chat_model_stream':
      if (data.chunk?.tool_call_chunks?.length > 0) {
        await processToolCallChunks(data.chunk.tool_call_chunks, state);
      }
      break;

    case 'on_tool_start':
      state.currentStep = `tool_${name}`;
      logStep(`🔧 调用工具: ${name}`, state);
      break;

    case 'on_tool_end':
      logStep(`✅ 工具完成: ${name}`, state);
      break;

    case 'on_chain_start':
      if (name && name !== '__start__') {
        state.currentStep = `chain_${name}`;
        logStep(`⚙️ 执行步骤: ${name}`, state);
      }
      break;

    case 'on_chain_end':
      if (name && name !== '__end__') {
        logStep(`✅ 步骤完成: ${name}`, state);
      }
      break;

    case 'on_chat_model_end':
      state.isThinking = false;
      state.currentStep = undefined;
      logStep('💭 思考完成', state);
      break;

    default:
      // 处理其他事件类型
      if (process.env.NODE_ENV === 'development') {
        console.log(`📡 Stream event: ${eventType}`, { name, data });
      }
  }
}

async function processToolCallChunks(toolCallChunks: any[], state: StreamState) {
  for (const { id, name, args } of toolCallChunks) {
    if (id && !state.toolCalls.has(id)) {
      state.toolCalls.set(id, { name, args: '' });
      logStep(`🛠️ 开始工具调用: ${name}`, state);
    }

    if (id && args) {
      const existingCall = state.toolCalls.get(id);
      if (existingCall) {
        existingCall.args += args;
      }
    }
  }
}

function logStep(message: string, state: StreamState) {
  state.intermediateSteps.push(message);

  if (process.env.NODE_ENV === 'development') {
    console.log(`[Stream] ${message}`);
  }

  // 可以在这里发送自定义事件给UI组件
  // 例如：发送当前步骤信息给ThinkingIndicator
}
